import { useState } from "react";
import { Layout } from "@/components/layout/Layout";
import { NotificationSettings } from "@/components/settings/NotificationSettings";
import { AppPreferences } from "@/components/settings/AppPreferences";
import { AccountSettings } from "@/components/settings/AccountSettings";
import { SecuritySettings } from "@/components/settings/SecuritySettings";
import { AboutSection } from "@/components/settings/AboutSection";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

// Mock data for development
const mockUser = {
  id: "current-user",
  name: "<PERSON>",
  username: "johndo<PERSON>",
  email: "<EMAIL>",
  phone: "+****************",
  avatar: "/placeholder.svg",
  joinedDate: "January 2023",
  isVerified: true,
};

const mockNotificationSettings = {
  pushNotifications: true,
  messageNotifications: true,
  callNotifications: true,
  groupNotifications: true,
  contactRequestNotifications: true,
  soundEnabled: true,
  vibrationEnabled: true,
  desktopNotifications: true,
  showMessagePreview: true,
  quietHoursEnabled: false,
  quietHoursStart: "22:00",
  quietHoursEnd: "08:00",
};

const mockAppPreferences = {
  theme: "system" as const,
  language: "en",
  autoDownloadImages: true,
  autoDownloadVideos: false,
  useWifiOnly: true,
  compressImages: true,
  saveToGallery: false,
  fontSize: "medium" as const,
  animationsEnabled: true,
};

const mockSecuritySettings = {
  twoFactorEnabled: false,
  biometricEnabled: true,
  sessionTimeout: 60,
  showReadReceipts: true,
  showTypingIndicator: true,
  allowScreenshots: true,
  encryptLocalStorage: true,
};

const mockActiveSessions = [
  {
    id: "1",
    device: "iPhone 14 Pro",
    location: "San Francisco, CA",
    lastActive: "Active now",
    isCurrent: true,
  },
  {
    id: "2",
    device: "MacBook Pro",
    location: "San Francisco, CA",
    lastActive: "2 hours ago",
    isCurrent: false,
  },
  {
    id: "3",
    device: "Chrome Browser",
    location: "New York, NY",
    lastActive: "3 days ago",
    isCurrent: false,
  },
];

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("account");
  const [notificationSettings, setNotificationSettings] = useState(mockNotificationSettings);
  const [appPreferences, setAppPreferences] = useState(mockAppPreferences);
  const [securitySettings, setSecuritySettings] = useState(mockSecuritySettings);
  const [activeSessions, setActiveSessions] = useState(mockActiveSessions);

  // Notification Settings Handlers
  const handleNotificationSettingChange = (setting: string, value: boolean | string) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: value
    }));
    console.log("Notification setting changed:", setting, value);
    // TODO: Implement actual settings save
  };

  // App Preferences Handlers
  const handleAppPreferenceChange = (setting: string, value: boolean | string) => {
    setAppPreferences(prev => ({
      ...prev,
      [setting]: value
    }));
    console.log("App preference changed:", setting, value);
    // TODO: Implement actual settings save
  };

  const handleClearCache = () => {
    console.log("Clearing cache...");
    // TODO: Implement cache clearing
  };

  const handleClearStorage = () => {
    console.log("Clearing all storage...");
    // TODO: Implement storage clearing with confirmation
  };

  // Account Settings Handlers
  const handleUpdateProfile = (data: any) => {
    console.log("Updating profile:", data);
    // TODO: Implement profile update
  };

  const handleChangeAvatar = (file: File) => {
    console.log("Changing avatar:", file);
    // TODO: Implement avatar upload
  };

  const handleLogout = () => {
    console.log("Logging out...");
    // TODO: Implement logout functionality
  };

  const handleDeleteAccount = () => {
    console.log("Deleting account...");
    // TODO: Implement account deletion
  };

  const handleDownloadData = () => {
    console.log("Downloading user data...");
    // TODO: Implement data export
  };

  // Security Settings Handlers
  const handleSecuritySettingChange = (setting: string, value: boolean | number) => {
    setSecuritySettings(prev => ({
      ...prev,
      [setting]: value
    }));
    console.log("Security setting changed:", setting, value);
    // TODO: Implement actual settings save
  };

  const handleChangePassword = (currentPassword: string, newPassword: string) => {
    console.log("Changing password...");
    // TODO: Implement password change
  };

  const handleTerminateSession = (sessionId: string) => {
    setActiveSessions(prev => prev.filter(session => session.id !== sessionId));
    console.log("Terminating session:", sessionId);
    // TODO: Implement session termination
  };

  const handleTerminateAllSessions = () => {
    setActiveSessions(prev => prev.filter(session => session.isCurrent));
    console.log("Terminating all sessions...");
    // TODO: Implement all sessions termination
  };

  // About Section Handlers
  const handleCheckUpdates = () => {
    console.log("Checking for updates...");
    // TODO: Implement update check
  };

  const handleViewChangelog = () => {
    window.open('/changelog', '_blank');
  };

  const handleContactSupport = () => {
    console.log("Contacting support...");
    // TODO: Open support modal or redirect
  };

  const handleReportBug = () => {
    console.log("Reporting bug...");
    // TODO: Open bug report form
  };

  const handleRateApp = () => {
    console.log("Rating app...");
    // TODO: Open app store rating
  };

  return (
    <Layout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center p-4 border-b border-border">
          <Button variant="ghost" size="sm" className="mr-3" onClick={() => window.history.back()}>
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <h1 className="text-xl font-semibold">Settings</h1>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-4">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="account">Account</TabsTrigger>
                <TabsTrigger value="security">Security</TabsTrigger>
                <TabsTrigger value="notifications">Notifications</TabsTrigger>
                <TabsTrigger value="preferences">Preferences</TabsTrigger>
                <TabsTrigger value="about">About</TabsTrigger>
              </TabsList>

              <TabsContent value="account" className="space-y-6">
                <AccountSettings
                  user={mockUser}
                  onUpdateProfile={handleUpdateProfile}
                  onChangeAvatar={handleChangeAvatar}
                  onLogout={handleLogout}
                  onDeleteAccount={handleDeleteAccount}
                  onDownloadData={handleDownloadData}
                />
              </TabsContent>

              <TabsContent value="security" className="space-y-6">
                <SecuritySettings
                  settings={securitySettings}
                  activeSessions={activeSessions}
                  onSettingChange={handleSecuritySettingChange}
                  onChangePassword={handleChangePassword}
                  onTerminateSession={handleTerminateSession}
                  onTerminateAllSessions={handleTerminateAllSessions}
                />
              </TabsContent>

              <TabsContent value="notifications" className="space-y-6">
                <NotificationSettings
                  settings={notificationSettings}
                  onSettingChange={handleNotificationSettingChange}
                />
              </TabsContent>

              <TabsContent value="preferences" className="space-y-6">
                <AppPreferences
                  settings={appPreferences}
                  onSettingChange={handleAppPreferenceChange}
                  onClearCache={handleClearCache}
                  onClearStorage={handleClearStorage}
                />
              </TabsContent>

              <TabsContent value="about" className="space-y-6">
                <AboutSection
                  appVersion="1.2.3"
                  buildNumber="2024.01.15.1"
                  onCheckUpdates={handleCheckUpdates}
                  onViewChangelog={handleViewChangelog}
                  onContactSupport={handleContactSupport}
                  onReportBug={handleReportBug}
                  onRateApp={handleRateApp}
                />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </Layout>
  );
}
