import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { 
  Palette, 
  Globe, 
  Download, 
  Wifi, 
  Smartphone,
  Trash2,
  Database,
  Moon,
  Sun,
  Monitor
} from "lucide-react";

interface AppPreferencesProps {
  settings: {
    theme: "light" | "dark" | "system";
    language: string;
    autoDownloadImages: boolean;
    autoDownloadVideos: boolean;
    useWifiOnly: boolean;
    compressImages: boolean;
    saveToGallery: boolean;
    fontSize: "small" | "medium" | "large";
    animationsEnabled: boolean;
  };
  onSettingChange: (setting: string, value: boolean | string) => void;
  onClearCache: () => void;
  onClearStorage: () => void;
}

export function AppPreferences({ 
  settings, 
  onSettingChange, 
  onClearCache, 
  onClearStorage 
}: AppPreferencesProps) {
  const getThemeIcon = (theme: string) => {
    switch (theme) {
      case "light":
        return <Sun className="w-4 h-4" />;
      case "dark":
        return <Moon className="w-4 h-4" />;
      default:
        return <Monitor className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Appearance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Palette className="w-5 h-5" />
            <span>Appearance</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Theme</Label>
            <Select
              value={settings.theme}
              onValueChange={(value) => onSettingChange("theme", value)}
            >
              <SelectTrigger className="w-full">
                <div className="flex items-center space-x-2">
                  {getThemeIcon(settings.theme)}
                  <SelectValue />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="light">
                  <div className="flex items-center space-x-2">
                    <Sun className="w-4 h-4" />
                    <span>Light</span>
                  </div>
                </SelectItem>
                <SelectItem value="dark">
                  <div className="flex items-center space-x-2">
                    <Moon className="w-4 h-4" />
                    <span>Dark</span>
                  </div>
                </SelectItem>
                <SelectItem value="system">
                  <div className="flex items-center space-x-2">
                    <Monitor className="w-4 h-4" />
                    <span>System</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Font Size</Label>
            <Select
              value={settings.fontSize}
              onValueChange={(value) => onSettingChange("fontSize", value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="small">Small</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="large">Large</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="animationsEnabled">Animations</Label>
              <p className="text-sm text-muted-foreground">
                Enable smooth animations and transitions
              </p>
            </div>
            <Switch
              id="animationsEnabled"
              checked={settings.animationsEnabled}
              onCheckedChange={(checked) => onSettingChange("animationsEnabled", checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Language */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="w-5 h-5" />
            <span>Language & Region</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Language</Label>
            <Select
              value={settings.language}
              onValueChange={(value) => onSettingChange("language", value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Español</SelectItem>
                <SelectItem value="fr">Français</SelectItem>
                <SelectItem value="de">Deutsch</SelectItem>
                <SelectItem value="it">Italiano</SelectItem>
                <SelectItem value="pt">Português</SelectItem>
                <SelectItem value="zh">中文</SelectItem>
                <SelectItem value="ja">日本語</SelectItem>
                <SelectItem value="ko">한국어</SelectItem>
                <SelectItem value="ar">العربية</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-sm text-muted-foreground">
              App will restart to apply language changes
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Media & Downloads */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Download className="w-5 h-5" />
            <span>Media & Downloads</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="autoDownloadImages">Auto-download Images</Label>
              <p className="text-sm text-muted-foreground">
                Automatically download images in chats
              </p>
            </div>
            <Switch
              id="autoDownloadImages"
              checked={settings.autoDownloadImages}
              onCheckedChange={(checked) => onSettingChange("autoDownloadImages", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="autoDownloadVideos">Auto-download Videos</Label>
              <p className="text-sm text-muted-foreground">
                Automatically download videos in chats
              </p>
            </div>
            <Switch
              id="autoDownloadVideos"
              checked={settings.autoDownloadVideos}
              onCheckedChange={(checked) => onSettingChange("autoDownloadVideos", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="useWifiOnly" className="flex items-center space-x-2">
                <Wifi className="w-4 h-4" />
                <span>Use Wi-Fi Only</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Only download media when connected to Wi-Fi
              </p>
            </div>
            <Switch
              id="useWifiOnly"
              checked={settings.useWifiOnly}
              onCheckedChange={(checked) => onSettingChange("useWifiOnly", checked)}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="compressImages">Compress Images</Label>
              <p className="text-sm text-muted-foreground">
                Reduce image file sizes to save bandwidth
              </p>
            </div>
            <Switch
              id="compressImages"
              checked={settings.compressImages}
              onCheckedChange={(checked) => onSettingChange("compressImages", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="saveToGallery" className="flex items-center space-x-2">
                <Smartphone className="w-4 h-4" />
                <span>Save to Gallery</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Save downloaded media to device gallery
              </p>
            </div>
            <Switch
              id="saveToGallery"
              checked={settings.saveToGallery}
              onCheckedChange={(checked) => onSettingChange("saveToGallery", checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Storage & Data */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="w-5 h-5" />
            <span>Storage & Data</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div>
              <h4 className="font-medium mb-2">Storage Usage</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex justify-between">
                  <span>Messages & Media</span>
                  <span>245 MB</span>
                </div>
                <div className="flex justify-between">
                  <span>Cache</span>
                  <span>89 MB</span>
                </div>
                <div className="flex justify-between">
                  <span>Total</span>
                  <span className="font-medium text-foreground">334 MB</span>
                </div>
              </div>
            </div>

            <Separator />

            <div className="flex flex-col space-y-2">
              <Button 
                variant="outline" 
                onClick={onClearCache}
                className="flex items-center space-x-2 justify-start"
              >
                <Trash2 className="w-4 h-4" />
                <span>Clear Cache</span>
              </Button>
              <p className="text-sm text-muted-foreground">
                Clear temporary files and cached data
              </p>
            </div>

            <div className="flex flex-col space-y-2">
              <Button 
                variant="outline" 
                onClick={onClearStorage}
                className="flex items-center space-x-2 justify-start text-destructive hover:text-destructive"
              >
                <Trash2 className="w-4 h-4" />
                <span>Clear All Data</span>
              </Button>
              <p className="text-sm text-muted-foreground">
                Remove all messages, media, and settings (irreversible)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
