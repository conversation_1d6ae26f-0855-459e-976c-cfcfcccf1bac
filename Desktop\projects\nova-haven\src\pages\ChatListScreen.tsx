import { useState } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { ChatPreviewItem } from "@/components/chat/ChatPreviewItem";
import { Search, Plus, Edit3 } from "lucide-react";

// Mock data for development
const mockChats = [
  {
    id: "1",
    name: "<PERSON>",
    avatar: "/placeholder.svg",
    lastMessage: "Hey! How are you doing today?",
    timestamp: "2m",
    unreadCount: 2,
    isOnline: true,
  },
  {
    id: "2",
    name: "Team Alpha",
    avatar: "/placeholder.svg",
    lastMessage: "Meeting scheduled for tomorrow at 10 AM",
    timestamp: "1h",
    unreadCount: 5,
    isOnline: false,
    isGroup: true,
  },
  {
    id: "3",
    name: "<PERSON>",
    avatar: "/placeholder.svg",
    lastMessage: "Thanks for the documents!",
    timestamp: "3h",
    unreadCount: 0,
    isOnline: true,
  },
  {
    id: "4",
    name: "Design Team",
    avatar: "/placeholder.svg",
    lastMessage: "New mockups are ready for review",
    timestamp: "5h",
    unreadCount: 1,
    isOnline: false,
    isGroup: true,
  },
  {
    id: "5",
    name: "Maria Garcia",
    avatar: "/placeholder.svg",
    lastMessage: "See you at the conference!",
    timestamp: "1d",
    unreadCount: 0,
    isOnline: false,
  },
  {
    id: "6",
    name: "John Doe",
    avatar: "/placeholder.svg",
    lastMessage: "Can we reschedule our call?",
    timestamp: "2d",
    unreadCount: 0,
    isOnline: true,
  },
];

export default function ChatListScreen() {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredChats = mockChats.filter(chat =>
    chat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    chat.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-3">
          <h1 className="text-xl font-semibold">Chats</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm">
            <Edit3 className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <Plus className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search chats..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto px-2 pb-20 lg:pb-4">
        {filteredChats.length > 0 ? (
          <div className="space-y-1">
            {filteredChats.map((chat) => (
              <ChatPreviewItem
                key={chat.id}
                id={chat.id}
                name={chat.name}
                avatar={chat.avatar}
                lastMessage={chat.lastMessage}
                timestamp={chat.timestamp}
                unreadCount={chat.unreadCount}
                isOnline={chat.isOnline}
                isGroup={chat.isGroup}
              />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
              <Search className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              No chats found
            </h3>
            <p className="text-muted-foreground max-w-xs">
              {searchQuery 
                ? `No chats match "${searchQuery}"`
                : "Start a new conversation to see your chats here"
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
