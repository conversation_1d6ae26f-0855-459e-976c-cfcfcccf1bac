@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * Tailwind CSS theme
   * tailwind.config.ts expects the following color variables to be expressed as HSL values.
   * A different format will require also updating the theme in tailwind.config.ts.
  */
  :root {
    --background: 0 0% 100%;
    --foreground: 220 13% 18%;

    --card: 0 0% 100%;
    --card-foreground: 220 13% 18%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;

    --primary: 220 100% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 13% 95%;
    --secondary-foreground: 220 13% 18%;

    --muted: 220 13% 95%;
    --muted-foreground: 220 13% 45%;

    --accent: 220 13% 95%;
    --accent-foreground: 220 13% 18%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 220 100% 50%;

    /* Chat-specific colors */
    --chat-bubble-sent: 220 100% 50%;
    --chat-bubble-sent-foreground: 0 0% 100%;
    --chat-bubble-received: 220 13% 95%;
    --chat-bubble-received-foreground: 220 13% 18%;
    --online-status: 142 76% 36%;
    --offline-status: 220 13% 60%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 220 13% 9%;
    --foreground: 220 13% 98%;

    --card: 220 13% 12%;
    --card-foreground: 220 13% 98%;

    --popover: 220 13% 12%;
    --popover-foreground: 220 13% 98%;

    --primary: 220 100% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 220 13% 15%;
    --secondary-foreground: 220 13% 98%;

    --muted: 220 13% 15%;
    --muted-foreground: 220 13% 60%;

    --accent: 220 13% 15%;
    --accent-foreground: 220 13% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 20%;
    --input: 220 13% 20%;
    --ring: 220 100% 50%;

    /* Chat-specific colors for dark mode */
    --chat-bubble-sent: 220 100% 50%;
    --chat-bubble-sent-foreground: 0 0% 100%;
    --chat-bubble-received: 220 13% 20%;
    --chat-bubble-received-foreground: 220 13% 98%;
    --online-status: 142 76% 36%;
    --offline-status: 220 13% 40%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
