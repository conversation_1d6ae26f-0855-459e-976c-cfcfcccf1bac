import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPin, MessageCircle, User, Calendar } from "lucide-react";
import { cn } from "@/lib/utils";
import { useNavigate } from "react-router-dom";

interface NearbyUser {
  id: string;
  name: string;
  username: string;
  avatar?: string;
  age?: number;
  gender?: "male" | "female" | "other";
  distance: string;
  isOnline: boolean;
  lastSeen?: string;
  bio?: string;
  mutualContacts?: number;
  showAge: boolean;
  showGender: boolean;
}

interface NearbyUserItemProps {
  user: NearbyUser;
  onViewProfile?: (userId: string) => void;
  onSendMessage?: (userId: string) => void;
}

export function NearbyUserItem({ user, onViewProfile, onSendMessage }: NearbyUserItemProps) {
  const navigate = useNavigate();

  const handleViewProfile = () => {
    if (onViewProfile) {
      onViewProfile(user.id);
    } else {
      navigate(`/profile/${user.id}`);
    }
  };

  const handleSendMessage = () => {
    if (onSendMessage) {
      onSendMessage(user.id);
    } else {
      navigate(`/chats/${user.id}`);
    }
  };

  const getGenderIcon = (gender?: string) => {
    switch (gender) {
      case "male":
        return "♂";
      case "female":
        return "♀";
      default:
        return "⚬";
    }
  };

  const getGenderColor = (gender?: string) => {
    switch (gender) {
      case "male":
        return "text-blue-500";
      case "female":
        return "text-pink-500";
      default:
        return "text-muted-foreground";
    }
  };

  return (
    <div className="flex items-center space-x-4 p-4 hover:bg-accent/50 rounded-lg transition-colors cursor-pointer group">
      {/* Avatar */}
      <div className="relative">
        <Avatar className="w-16 h-16">
          <AvatarImage src={user.avatar} alt={user.name} />
          <AvatarFallback className="bg-primary/10 text-primary font-medium text-lg">
            {user.name.slice(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div className={cn(
          "absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-background",
          user.isOnline ? "bg-status-online" : "bg-status-offline"
        )} />
      </div>

      {/* User Info */}
      <div className="flex-1 min-w-0 space-y-1">
        <div className="flex items-center space-x-2">
          <h3 className="font-semibold text-foreground truncate">
            {user.name}
          </h3>
          {user.showAge && user.age && (
            <Badge variant="secondary" className="text-xs px-2 py-0.5">
              <Calendar className="w-3 h-3 mr-1" />
              {user.age}
            </Badge>
          )}
          {user.showGender && user.gender && (
            <span className={cn("text-sm font-medium", getGenderColor(user.gender))}>
              {getGenderIcon(user.gender)}
            </span>
          )}
        </div>

        <p className="text-sm text-muted-foreground">@{user.username}</p>

        {user.bio && (
          <p className="text-sm text-foreground line-clamp-2 leading-relaxed">
            {user.bio}
          </p>
        )}

        <div className="flex items-center space-x-4 text-xs text-muted-foreground">
          <div className="flex items-center space-x-1">
            <MapPin className="w-3 h-3" />
            <span>{user.distance} away</span>
          </div>
          {user.mutualContacts && user.mutualContacts > 0 && (
            <div className="flex items-center space-x-1">
              <User className="w-3 h-3" />
              <span>{user.mutualContacts} mutual contact{user.mutualContacts > 1 ? 's' : ''}</span>
            </div>
          )}
          {user.isOnline ? (
            <span className="text-status-online">Online</span>
          ) : user.lastSeen ? (
            <span>Last seen {user.lastSeen}</span>
          ) : null}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <Button
          size="sm"
          variant="outline"
          onClick={handleViewProfile}
          className="flex items-center space-x-1 text-xs"
        >
          <User className="w-3 h-3" />
          <span>View Profile</span>
        </Button>
        <Button
          size="sm"
          onClick={handleSendMessage}
          className="flex items-center space-x-1 text-xs"
        >
          <MessageCircle className="w-3 h-3" />
          <span>Message</span>
        </Button>
      </div>
    </div>
  );
}
