import { useState } from "react";
import { Layout } from "@/components/layout/Layout";
import { NearbyListView } from "@/components/nearby/NearbyListView";
import { NearbyMapView } from "@/components/nearby/NearbyMapView";
import { UserPreferencesForm } from "@/components/nearby/UserPreferencesForm";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Map, 
  List, 
  Settings, 
  MapPin,
  Filter,
  X
} from "lucide-react";

// Default preferences
const defaultPreferences = {
  genderPreference: "any" as const,
  ageRange: [18, 99],
  maxDistance: 10,
  showOnlineOnly: false,
  showWithPhotosOnly: false,
  showMutualContactsOnly: false,
};

export default function NearbyPage() {
  const [activeTab, setActiveTab] = useState("list");
  const [showPreferences, setShowPreferences] = useState(false);
  const [preferences, setPreferences] = useState(defaultPreferences);
  const [hasActiveFilters, setHasActiveFilters] = useState(false);

  const handleOpenPreferences = () => {
    setShowPreferences(true);
  };

  const handleClosePreferences = () => {
    setShowPreferences(false);
  };

  const handleSavePreferences = (newPreferences: typeof preferences) => {
    setPreferences(newPreferences);
    
    // Check if any non-default filters are active
    const isFiltered = 
      newPreferences.genderPreference !== "any" ||
      newPreferences.ageRange[0] !== 18 ||
      newPreferences.ageRange[1] !== 99 ||
      newPreferences.maxDistance !== 10 ||
      newPreferences.showOnlineOnly ||
      newPreferences.showWithPhotosOnly ||
      newPreferences.showMutualContactsOnly;
    
    setHasActiveFilters(isFiltered);
    console.log("Preferences saved:", newPreferences);
    // TODO: Apply filters to user search
  };

  const handleRefresh = () => {
    console.log("Refreshing nearby users...");
    // TODO: Implement location refresh and user search
  };

  const handleUserClick = (userId: string) => {
    console.log("User clicked:", userId);
    // TODO: Handle user selection (show profile, etc.)
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (preferences.genderPreference !== "any") count++;
    if (preferences.ageRange[0] !== 18 || preferences.ageRange[1] !== 99) count++;
    if (preferences.maxDistance !== 10) count++;
    if (preferences.showOnlineOnly) count++;
    if (preferences.showWithPhotosOnly) count++;
    if (preferences.showMutualContactsOnly) count++;
    return count;
  };

  // Show preferences overlay
  if (showPreferences) {
    return (
      <Layout>
        <div className="flex flex-col h-full bg-background">
          <div className="flex-1 overflow-y-auto p-4 flex items-center justify-center">
            <UserPreferencesForm
              initialPreferences={preferences}
              onSave={handleSavePreferences}
              onClose={handleClosePreferences}
            />
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <div className="flex items-center space-x-3">
            <h1 className="text-xl font-semibold">Nearby</h1>
            <Badge variant="secondary" className="flex items-center space-x-1">
              <MapPin className="w-3 h-3" />
              <span>San Francisco</span>
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant={hasActiveFilters ? "default" : "outline"}
              size="sm"
              onClick={handleOpenPreferences}
              className="relative"
            >
              <Filter className="w-4 h-4" />
              {hasActiveFilters && (
                <Badge className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs">
                  {getActiveFiltersCount()}
                </Badge>
              )}
            </Button>
            <Button variant="ghost" size="sm">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="px-4 py-2 bg-muted/30 border-b border-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-sm">
                <Filter className="w-4 h-4 text-muted-foreground" />
                <span className="text-muted-foreground">Active filters:</span>
                <div className="flex items-center space-x-2">
                  {preferences.genderPreference !== "any" && (
                    <Badge variant="secondary" className="text-xs">
                      Gender: {preferences.genderPreference}
                    </Badge>
                  )}
                  {(preferences.ageRange[0] !== 18 || preferences.ageRange[1] !== 99) && (
                    <Badge variant="secondary" className="text-xs">
                      Age: {preferences.ageRange[0]}-{preferences.ageRange[1]}
                    </Badge>
                  )}
                  {preferences.maxDistance !== 10 && (
                    <Badge variant="secondary" className="text-xs">
                      Distance: {preferences.maxDistance}km
                    </Badge>
                  )}
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSavePreferences(defaultPreferences)}
                className="text-xs h-6 px-2"
              >
                <X className="w-3 h-3 mr-1" />
                Clear
              </Button>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="w-full justify-start p-1 m-4 mb-0">
              <TabsTrigger value="list" className="flex items-center space-x-2">
                <List className="w-4 h-4" />
                <span>List View</span>
              </TabsTrigger>
              <TabsTrigger value="map" className="flex items-center space-x-2">
                <Map className="w-4 h-4" />
                <span>Map View</span>
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-hidden">
              <TabsContent value="list" className="h-full mt-0">
                <NearbyListView
                  onRefresh={handleRefresh}
                  onOpenFilters={handleOpenPreferences}
                />
              </TabsContent>

              <TabsContent value="map" className="h-full mt-0">
                <NearbyMapView
                  onUserClick={handleUserClick}
                  onRefresh={handleRefresh}
                />
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </Layout>
  );
}
