import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Info, 
  MessageCircle, 
  ExternalLink, 
  FileText, 
  Shield, 
  Heart,
  Github,
  Twitter,
  Mail,
  Star
} from "lucide-react";

interface AboutSectionProps {
  appVersion: string;
  buildNumber: string;
  onCheckUpdates: () => void;
  onViewChangelog: () => void;
  onContactSupport: () => void;
  onReportBug: () => void;
  onRateApp: () => void;
}

export function AboutSection({ 
  appVersion,
  buildNumber,
  onCheckUpdates,
  onViewChangelog,
  onContactSupport,
  onReportBug,
  onRateApp
}: AboutSectionProps) {
  const currentYear = new Date().getFullYear();

  return (
    <div className="space-y-6">
      {/* App Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Info className="w-5 h-5" />
            <span>App Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-center pb-4">
            <div className="text-center space-y-3">
              <div className="w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mx-auto">
                <MessageCircle className="w-8 h-8 text-primary-foreground" />
              </div>
              <div>
                <h3 className="text-xl font-bold">ChatApp</h3>
                <p className="text-muted-foreground">Connect with people worldwide</p>
              </div>
            </div>
          </div>

          <Separator />

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Version</span>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary">{appVersion}</Badge>
                <Button variant="outline" size="sm" onClick={onCheckUpdates}>
                  Check Updates
                </Button>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Build</span>
              <span className="font-mono text-sm">{buildNumber}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">Platform</span>
              <span>{navigator.platform}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-muted-foreground">User Agent</span>
              <span className="text-sm max-w-32 truncate" title={navigator.userAgent}>
                {navigator.userAgent.split(' ')[0]}
              </span>
            </div>
          </div>

          <Separator />

          <Button 
            variant="outline" 
            onClick={onViewChangelog}
            className="w-full justify-start"
          >
            <FileText className="w-4 h-4 mr-2" />
            View Changelog
          </Button>
        </CardContent>
      </Card>

      {/* Help & Support */}
      <Card>
        <CardHeader>
          <CardTitle>Help & Support</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Button 
            variant="outline" 
            onClick={onContactSupport}
            className="w-full justify-start"
          >
            <Mail className="w-4 h-4 mr-2" />
            Contact Support
          </Button>

          <Button 
            variant="outline" 
            onClick={onReportBug}
            className="w-full justify-start"
          >
            <Shield className="w-4 h-4 mr-2" />
            Report a Bug
          </Button>

          <Button 
            variant="outline" 
            onClick={onRateApp}
            className="w-full justify-start"
          >
            <Star className="w-4 h-4 mr-2" />
            Rate the App
          </Button>

          <Button 
            variant="outline" 
            onClick={() => window.open('https://docs.chatapp.com', '_blank')}
            className="w-full justify-start"
          >
            <FileText className="w-4 h-4 mr-2" />
            Documentation
            <ExternalLink className="w-3 h-3 ml-auto" />
          </Button>
        </CardContent>
      </Card>

      {/* Legal & Privacy */}
      <Card>
        <CardHeader>
          <CardTitle>Legal & Privacy</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Button 
            variant="outline" 
            onClick={() => window.open('/privacy-policy', '_blank')}
            className="w-full justify-start"
          >
            <Shield className="w-4 h-4 mr-2" />
            Privacy Policy
            <ExternalLink className="w-3 h-3 ml-auto" />
          </Button>

          <Button 
            variant="outline" 
            onClick={() => window.open('/terms-of-service', '_blank')}
            className="w-full justify-start"
          >
            <FileText className="w-4 h-4 mr-2" />
            Terms of Service
            <ExternalLink className="w-3 h-3 ml-auto" />
          </Button>

          <Button 
            variant="outline" 
            onClick={() => window.open('/licenses', '_blank')}
            className="w-full justify-start"
          >
            <FileText className="w-4 h-4 mr-2" />
            Open Source Licenses
            <ExternalLink className="w-3 h-3 ml-auto" />
          </Button>
        </CardContent>
      </Card>

      {/* Social & Community */}
      <Card>
        <CardHeader>
          <CardTitle>Community</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Button 
            variant="outline" 
            onClick={() => window.open('https://github.com/chatapp', '_blank')}
            className="w-full justify-start"
          >
            <Github className="w-4 h-4 mr-2" />
            GitHub
            <ExternalLink className="w-3 h-3 ml-auto" />
          </Button>

          <Button 
            variant="outline" 
            onClick={() => window.open('https://twitter.com/chatapp', '_blank')}
            className="w-full justify-start"
          >
            <Twitter className="w-4 h-4 mr-2" />
            Twitter
            <ExternalLink className="w-3 h-3 ml-auto" />
          </Button>

          <Button 
            variant="outline" 
            onClick={() => window.open('https://discord.gg/chatapp', '_blank')}
            className="w-full justify-start"
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            Discord Community
            <ExternalLink className="w-3 h-3 ml-auto" />
          </Button>
        </CardContent>
      </Card>

      {/* Credits & Acknowledgments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Heart className="w-5 h-5" />
            <span>Credits</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center space-y-2">
            <p className="text-sm text-muted-foreground">
              Made with ❤️ by the ChatApp team
            </p>
            <p className="text-sm text-muted-foreground">
              Built with React, TypeScript, and Tailwind CSS
            </p>
          </div>

          <Separator />

          <div className="space-y-2">
            <h4 className="font-medium text-sm">Special Thanks</h4>
            <div className="text-sm text-muted-foreground space-y-1">
              <p>• React team for the amazing framework</p>
              <p>• Radix UI for accessible components</p>
              <p>• Lucide for beautiful icons</p>
              <p>• All our beta testers and contributors</p>
            </div>
          </div>

          <Separator />

          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              © {currentYear} ChatApp. All rights reserved.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
