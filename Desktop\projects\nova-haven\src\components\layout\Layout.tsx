import { useDesktop } from "@/hooks/use-media-query";
import { Sidebar } from "./Sidebar";
import { BottomNavigation } from "./BottomNavigation";
import { useLocation } from "react-router-dom";

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const isDesktop = useDesktop();
  const location = useLocation();

  // Hide bottom navigation in chat routes
  const isChatRoute = location.pathname.startsWith('/chats/');

  return (
    <div className="h-screen flex bg-background">
      {isDesktop && <Sidebar />}

      <main className="flex-1 flex flex-col overflow-hidden">
        {children}
      </main>

      {!isDesktop && !isChatRoute && <BottomNavigation />}
    </div>
  );
}
