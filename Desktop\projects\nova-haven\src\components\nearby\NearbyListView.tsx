import { useState } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { NearbyUserItem } from "./NearbyUserItem";
import { Search, Filter, MapPin, RefreshCw } from "lucide-react";

// Mock data for development
const mockNearbyUsers = [
  {
    id: "1",
    name: "<PERSON>",
    username: "emily.w",
    avatar: "/placeholder.svg",
    age: 28,
    gender: "female" as const,
    distance: "0.3 km",
    isOnline: true,
    bio: "Love hiking and photography 📸 Always looking for new adventures!",
    mutualContacts: 2,
    showAge: true,
    showGender: true,
  },
  {
    id: "2", 
    name: "<PERSON>",
    username: "alex.t",
    avatar: "/placeholder.svg",
    age: 31,
    gender: "male" as const,
    distance: "0.8 km",
    isOnline: false,
    lastSeen: "2h ago",
    bio: "Software engineer by day, musician by night 🎸",
    mutualContacts: 1,
    showAge: true,
    showGender: true,
  },
  {
    id: "3",
    name: "<PERSON>",
    username: "sarah.c",
    avatar: "/placeholder.svg",
    age: 26,
    gender: "female" as const,
    distance: "1.2 km",
    isOnline: true,
    bio: "Coffee enthusiast ☕ Dog lover 🐕 Always up for a good conversation!",
    mutualContacts: 0,
    showAge: true,
    showGender: true,
  },
  {
    id: "4",
    name: "Michael Rodriguez",
    username: "mike.r",
    avatar: "/placeholder.svg",
    age: 29,
    gender: "male" as const,
    distance: "1.5 km",
    isOnline: false,
    lastSeen: "1d ago",
    bio: "Fitness trainer and nutrition enthusiast. Let's stay healthy together! 💪",
    mutualContacts: 3,
    showAge: true,
    showGender: true,
  },
  {
    id: "5",
    name: "Jessica Park",
    username: "jess.p",
    avatar: "/placeholder.svg",
    age: 24,
    gender: "female" as const,
    distance: "2.1 km",
    isOnline: true,
    bio: "Art student, nature lover, and weekend explorer 🌿",
    mutualContacts: 0,
    showAge: true,
    showGender: true,
  },
  {
    id: "6",
    name: "David Kim",
    username: "david.k",
    avatar: "/placeholder.svg",
    age: 33,
    gender: "male" as const,
    distance: "2.8 km",
    isOnline: false,
    lastSeen: "5h ago",
    bio: "Foodie and travel enthusiast. Always seeking the next great adventure! ✈️",
    mutualContacts: 1,
    showAge: true,
    showGender: true,
  },
];

interface NearbyListViewProps {
  onRefresh?: () => void;
  onOpenFilters?: () => void;
}

export function NearbyListView({ onRefresh, onOpenFilters }: NearbyListViewProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isRefreshing, setIsRefreshing] = useState(false);

  const filteredUsers = mockNearbyUsers.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.bio?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setIsRefreshing(false);
      onRefresh?.();
    }, 1000);
  };

  const handleViewProfile = (userId: string) => {
    console.log("View profile:", userId);
    // TODO: Navigate to user profile or open profile modal
  };

  const handleSendMessage = (userId: string) => {
    console.log("Send message to:", userId);
    // TODO: Navigate to chat or open message dialog
  };

  return (
    <div className="flex flex-col h-full">
      {/* Search and Actions */}
      <div className="p-4 space-y-3 border-b border-border">
        <div className="flex space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search nearby users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline" size="sm" onClick={onOpenFilters}>
            <Filter className="w-4 h-4" />
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
          <div className="flex items-center space-x-1">
            <MapPin className="w-4 h-4" />
            <span>{filteredUsers.length} users nearby</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-status-online rounded-full" />
            <span>{filteredUsers.filter(u => u.isOnline).length} online</span>
          </div>
        </div>
      </div>

      {/* Users List */}
      <div className="flex-1 overflow-y-auto pb-20 lg:pb-4">
        {filteredUsers.length > 0 ? (
          <div className="divide-y divide-border">
            {filteredUsers.map((user) => (
              <NearbyUserItem
                key={user.id}
                user={user}
                onViewProfile={handleViewProfile}
                onSendMessage={handleSendMessage}
              />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-64 text-center p-8">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
              {searchQuery ? (
                <Search className="w-8 h-8 text-muted-foreground" />
              ) : (
                <MapPin className="w-8 h-8 text-muted-foreground" />
              )}
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              {searchQuery ? "No users found" : "No nearby users"}
            </h3>
            <p className="text-muted-foreground max-w-xs">
              {searchQuery 
                ? `No users match "${searchQuery}". Try adjusting your search.`
                : "There are no users in your area right now. Try refreshing or expanding your search radius."
              }
            </p>
            {!searchQuery && (
              <Button 
                onClick={handleRefresh} 
                className="mt-4"
                disabled={isRefreshing}
              >
                <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
