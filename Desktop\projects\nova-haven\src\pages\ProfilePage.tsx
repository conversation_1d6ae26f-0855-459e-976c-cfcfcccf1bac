import { useState } from "react";
import { Layout } from "@/components/layout/Layout";
import { UserProfileDisplay } from "@/components/profile/UserProfileDisplay";
import { EditProfileForm } from "@/components/profile/EditProfileForm";
import { ProfilePhotoManager } from "@/components/profile/ProfilePhotoManager";
import { PrivacySettings } from "@/components/profile/PrivacySettings";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

// Mock data for development
const mockUserProfile = {
  id: "current-user",
  name: "<PERSON>",
  username: "johndo<PERSON>",
  avatar: "/placeholder.svg",
  bio: "Software developer passionate about creating amazing user experiences. Love hiking, photography, and good coffee ☕",
  location: "San Francisco, CA",
  birthday: "March 15, 1995",
  isOnline: true,
  joinedDate: "January 2023",
  additionalPhotos: [
    "/placeholder.svg",
    "/placeholder.svg",
    "/placeholder.svg",
  ],
  showBirthday: true,
  showGender: true,
  gender: "male" as const,
};

const mockPhotos = [
  {
    id: "1",
    url: "/placeholder.svg",
    isAvatar: true,
    uploadedAt: new Date(),
  },
  {
    id: "2",
    url: "/placeholder.svg",
    isAvatar: false,
    uploadedAt: new Date(),
  },
  {
    id: "3",
    url: "/placeholder.svg",
    isAvatar: false,
    uploadedAt: new Date(),
  },
];

const mockPrivacySettings = {
  hideOnlineStatus: false,
  hideLastSeen: false,
  allowMessagesFromContacts: true,
  allowContactRequests: true,
  showProfileToNearby: true,
  showBirthday: true,
  showGender: true,
};

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState("profile");
  const [isEditing, setIsEditing] = useState(false);
  const [isManagingPhotos, setIsManagingPhotos] = useState(false);
  const [profile, setProfile] = useState(mockUserProfile);
  const [photos, setPhotos] = useState(mockPhotos);
  const [privacySettings, setPrivacySettings] = useState(mockPrivacySettings);

  // Handle profile editing
  const handleEditProfile = () => {
    setIsEditing(true);
  };

  const handleSaveProfile = (data: any) => {
    console.log("Saving profile:", data);
    setProfile({ ...profile, ...data });
    setIsEditing(false);
    // TODO: Implement actual save functionality
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  // Handle photo management
  const handleAddPhotos = () => {
    setIsManagingPhotos(true);
  };

  const handlePhotosUpload = (files: FileList) => {
    console.log("Uploading photos:", files);
    // TODO: Implement actual upload functionality
  };

  const handleDeletePhoto = (photoId: string) => {
    setPhotos(photos.filter(photo => photo.id !== photoId));
    console.log("Deleting photo:", photoId);
    // TODO: Implement actual delete functionality
  };

  const handleSetAsAvatar = (photoId: string) => {
    setPhotos(photos.map(photo => ({
      ...photo,
      isAvatar: photo.id === photoId
    })));
    console.log("Setting avatar:", photoId);
    // TODO: Implement actual avatar update functionality
  };

  const handleClosePhotoManager = () => {
    setIsManagingPhotos(false);
  };

  // Handle privacy settings
  const handlePrivacySettingChange = (setting: string, value: boolean) => {
    setPrivacySettings(prev => ({
      ...prev,
      [setting]: value
    }));
    console.log("Privacy setting changed:", setting, value);
    // TODO: Implement actual settings save functionality
  };

  const handlePhotoClick = (photoIndex: number) => {
    console.log("Photo clicked:", photoIndex);
    // TODO: Implement photo viewer/gallery
  };

  // Show photo manager overlay
  if (isManagingPhotos) {
    return (
      <Layout>
        <div className="flex flex-col h-full bg-background">
          <div className="flex-1 overflow-y-auto p-4">
            <ProfilePhotoManager
              photos={photos}
              currentAvatarId={photos.find(p => p.isAvatar)?.id}
              onPhotosUpload={handlePhotosUpload}
              onDeletePhoto={handleDeletePhoto}
              onSetAsAvatar={handleSetAsAvatar}
              onClose={handleClosePhotoManager}
            />
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center p-4 border-b border-border">
          <Button variant="ghost" size="sm" className="mr-3" onClick={() => window.history.back()}>
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <h1 className="text-xl font-semibold">Profile</h1>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-4">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="profile">Profile</TabsTrigger>
                <TabsTrigger value="edit">Edit</TabsTrigger>
                <TabsTrigger value="privacy">Privacy</TabsTrigger>
              </TabsList>

              <TabsContent value="profile" className="space-y-6">
                {!isEditing ? (
                  <UserProfileDisplay
                    profile={profile}
                    isOwnProfile={true}
                    onEditProfile={handleEditProfile}
                    onAddPhotos={handleAddPhotos}
                    onPhotoClick={handlePhotoClick}
                  />
                ) : (
                  <EditProfileForm
                    initialData={{
                      name: profile.name,
                      username: profile.username,
                      bio: profile.bio,
                      location: profile.location,
                      gender: profile.gender,
                      birthday: profile.birthday ? new Date(profile.birthday) : undefined,
                      showBirthday: profile.showBirthday,
                      showGender: profile.showGender,
                    }}
                    onSave={handleSaveProfile}
                    onCancel={handleCancelEdit}
                  />
                )}
              </TabsContent>

              <TabsContent value="edit" className="space-y-6">
                <EditProfileForm
                  initialData={{
                    name: profile.name,
                    username: profile.username,
                    bio: profile.bio,
                    location: profile.location,
                    gender: profile.gender,
                    birthday: profile.birthday ? new Date(profile.birthday) : undefined,
                    showBirthday: profile.showBirthday,
                    showGender: profile.showGender,
                  }}
                  onSave={handleSaveProfile}
                  onCancel={() => setActiveTab("profile")}
                />
              </TabsContent>

              <TabsContent value="privacy" className="space-y-6">
                <PrivacySettings
                  settings={privacySettings}
                  onSettingChange={handlePrivacySettingChange}
                />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </Layout>
  );
}
