import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Check, 
  CheckCheck, 
  Download, 
  Play,
  MapPin,
  FileText,
  Image as ImageIcon,
  Video as VideoIcon,
  Clock
} from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface MessageContent {
  type: "text" | "image" | "video" | "file" | "location";
  text?: string;
  imageUrl?: string;
  videoUrl?: string;
  fileName?: string;
  fileUrl?: string;
  fileSize?: string;
  location?: {
    lat: number;
    lng: number;
    name: string;
    isLive?: boolean;
  };
}

interface Message {
  id: string;
  content: MessageContent;
  timestamp: Date;
  isSentByMe: boolean;
  isDelivered?: boolean;
  isRead?: boolean;
  senderName?: string;
  senderAvatar?: string;
}

interface MessageBubbleProps {
  message: Message;
  showAvatar?: boolean;
  isGroupChat?: boolean;
  onImageClick?: (imageUrl: string) => void;
  onFileDownload?: (fileUrl: string, fileName: string) => void;
  onLocationClick?: (location: any) => void;
}

export function MessageBubble({
  message,
  showAvatar = false,
  isGroupChat = false,
  onImageClick,
  onFileDownload,
  onLocationClick,
}: MessageBubbleProps) {
  const { content, timestamp, isSentByMe, isDelivered, isRead, senderName, senderAvatar } = message;

  const renderMessageContent = () => {
    switch (content.type) {
      case "text":
        return (
          <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
            {content.text}
          </p>
        );

      case "image":
        return (
          <div className="space-y-2">
            <div 
              className="relative rounded-lg overflow-hidden cursor-pointer max-w-xs"
              onClick={() => onImageClick?.(content.imageUrl!)}
            >
              <img
                src={content.imageUrl}
                alt="Shared image"
                className="w-full h-auto object-cover hover:opacity-90 transition-opacity"
              />
              <div className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors flex items-center justify-center">
                <ImageIcon className="w-6 h-6 text-white opacity-0 hover:opacity-100 transition-opacity" />
              </div>
            </div>
            {content.text && (
              <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                {content.text}
              </p>
            )}
          </div>
        );

      case "video":
        return (
          <div className="space-y-2">
            <div className="relative rounded-lg overflow-hidden max-w-xs">
              <video
                src={content.videoUrl}
                className="w-full h-auto object-cover"
                controls
              />
            </div>
            {content.text && (
              <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                {content.text}
              </p>
            )}
          </div>
        );

      case "file":
        return (
          <div className="space-y-2">
            <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg max-w-xs">
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <FileText className="w-5 h-5 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{content.fileName}</p>
                {content.fileSize && (
                  <p className="text-xs text-muted-foreground">{content.fileSize}</p>
                )}
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onFileDownload?.(content.fileUrl!, content.fileName!)}
              >
                <Download className="w-4 h-4" />
              </Button>
            </div>
            {content.text && (
              <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                {content.text}
              </p>
            )}
          </div>
        );

      case "location":
        return (
          <div className="space-y-2">
            <div 
              className="flex items-center space-x-3 p-3 bg-muted/50 rounded-lg max-w-xs cursor-pointer hover:bg-muted/70 transition-colors"
              onClick={() => onLocationClick?.(content.location)}
            >
              <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                <MapPin className="w-5 h-5 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{content.location?.name}</p>
                <div className="flex items-center space-x-2">
                  <p className="text-xs text-muted-foreground">Location</p>
                  {content.location?.isLive && (
                    <Badge variant="secondary" className="text-xs px-1 py-0">
                      <div className="w-1.5 h-1.5 bg-red-500 rounded-full mr-1 animate-pulse" />
                      Live
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            {content.text && (
              <p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
                {content.text}
              </p>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  const getMessageStatus = () => {
    if (!isSentByMe) return null;
    
    if (isRead) {
      return <CheckCheck className="w-3 h-3 text-blue-500" />;
    } else if (isDelivered) {
      return <CheckCheck className="w-3 h-3 text-muted-foreground" />;
    } else {
      return <Check className="w-3 h-3 text-muted-foreground" />;
    }
  };

  return (
    <div className={cn(
      "flex items-end space-x-2 mb-4",
      isSentByMe ? "justify-end" : "justify-start"
    )}>
      {/* Avatar for received messages in group chats */}
      {!isSentByMe && showAvatar && isGroupChat && (
        <Avatar className="w-6 h-6 mb-1">
          <AvatarImage src={senderAvatar} alt={senderName} />
          <AvatarFallback className="text-xs">
            {senderName?.slice(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
      )}

      <div className={cn(
        "max-w-[70%] space-y-1",
        isSentByMe ? "items-end" : "items-start"
      )}>
        {/* Sender name for group chats */}
        {!isSentByMe && isGroupChat && senderName && (
          <p className="text-xs text-muted-foreground px-2">{senderName}</p>
        )}

        {/* Message bubble */}
        <div className={cn(
          "rounded-2xl px-4 py-2 shadow-sm",
          isSentByMe 
            ? "bg-chat-bubble-sent text-chat-bubble-sent-foreground rounded-br-md" 
            : "bg-chat-bubble-received text-chat-bubble-received-foreground rounded-bl-md"
        )}>
          {renderMessageContent()}
        </div>

        {/* Timestamp and status */}
        <div className={cn(
          "flex items-center space-x-1 px-2",
          isSentByMe ? "justify-end" : "justify-start"
        )}>
          <span className="text-xs text-muted-foreground">
            {format(timestamp, "HH:mm")}
          </span>
          {getMessageStatus()}
        </div>
      </div>

      {/* Spacer for sent messages to balance layout */}
      {isSentByMe && showAvatar && isGroupChat && (
        <div className="w-6" />
      )}
    </div>
  );
}
