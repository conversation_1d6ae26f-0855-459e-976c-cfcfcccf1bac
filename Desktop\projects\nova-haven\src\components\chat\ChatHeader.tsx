import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  Phone, 
  Video, 
  MoreVertical,
  Info
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ChatUser {
  id: string;
  name: string;
  username: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: string;
  isTyping?: boolean;
}

interface ChatHeaderProps {
  user: ChatUser;
  onBack?: () => void;
  onCall?: () => void;
  onVideoCall?: () => void;
  onViewProfile?: () => void;
  onBlockUser?: () => void;
  onReportUser?: () => void;
  onClearChat?: () => void;
}

export function ChatHeader({
  user,
  onBack,
  onCall,
  onVideoCall,
  onViewProfile,
  onBlockUser,
  onReportUser,
  onClearChat,
}: ChatHeaderProps) {
  const getStatusText = () => {
    if (user.isTyping) return "typing...";
    if (user.isOnline) return "Online";
    if (user.lastSeen) return `Last seen ${user.lastSeen}`;
    return "Offline";
  };

  const getStatusColor = () => {
    if (user.isTyping) return "text-primary";
    if (user.isOnline) return "text-status-online";
    return "text-muted-foreground";
  };

  return (
    <div className="flex items-center justify-between p-4 border-b border-border bg-background">
      {/* Left Section - Back Button + User Info */}
      <div className="flex items-center space-x-3">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onBack}
          className="lg:hidden"
        >
          <ArrowLeft className="w-4 h-4" />
        </Button>
        
        <div className="flex items-center space-x-3 cursor-pointer" onClick={onViewProfile}>
          <div className="relative">
            <Avatar className="w-10 h-10">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback className="bg-primary/10 text-primary font-medium">
                {user.name.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className={cn(
              "absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background",
              user.isOnline ? "bg-status-online" : "bg-status-offline"
            )} />
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-foreground truncate">
              {user.name}
            </h3>
            <p className={cn("text-sm truncate", getStatusColor())}>
              {getStatusText()}
            </p>
          </div>
        </div>
      </div>

      {/* Right Section - Action Buttons */}
      <div className="flex items-center space-x-2">
        <Button 
          variant="ghost" 
          size="sm"
          onClick={onCall}
          className="hidden sm:flex"
        >
          <Phone className="w-4 h-4" />
        </Button>
        
        <Button 
          variant="ghost" 
          size="sm"
          onClick={onVideoCall}
          className="hidden sm:flex"
        >
          <Video className="w-4 h-4" />
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreVertical className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={onViewProfile}>
              <Info className="w-4 h-4 mr-2" />
              View Profile
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onCall} className="sm:hidden">
              <Phone className="w-4 h-4 mr-2" />
              Voice Call
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onVideoCall} className="sm:hidden">
              <Video className="w-4 h-4 mr-2" />
              Video Call
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onClearChat}>
              Clear Chat
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={onBlockUser}
              className="text-destructive focus:text-destructive"
            >
              Block User
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={onReportUser}
              className="text-destructive focus:text-destructive"
            >
              Report User
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
