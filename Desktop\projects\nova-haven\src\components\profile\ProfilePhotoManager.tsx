import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Upload, 
  X, 
  Star, 
  Camera, 
  Image as ImageIcon,
  Trash2,
  Crown
} from "lucide-react";
import { cn } from "@/lib/utils";

interface Photo {
  id: string;
  url: string;
  isAvatar: boolean;
  uploadedAt: Date;
}

interface ProfilePhotoManagerProps {
  photos: Photo[];
  currentAvatarId?: string;
  onPhotosUpload: (files: FileList) => void;
  onDeletePhoto: (photoId: string) => void;
  onSetAsAvatar: (photoId: string) => void;
  onClose: () => void;
  maxPhotos?: number;
}

export function ProfilePhotoManager({
  photos,
  currentAvatarId,
  onPhotosUpload,
  onDeletePhoto,
  onSetAsAvatar,
  onClose,
  maxPhotos = 10,
}: ProfilePhotoManagerProps) {
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      onPhotosUpload(files);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      onPhotosUpload(files);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const canUploadMore = photos.length < maxPhotos;

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Camera className="w-5 h-5" />
            <span>Manage Photos</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Upload Area */}
        {canUploadMore && (
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer",
              dragOver 
                ? "border-primary bg-primary/5" 
                : "border-muted-foreground/25 hover:border-muted-foreground/50"
            )}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={openFileDialog}
          >
            <div className="space-y-4">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
                <Upload className="w-8 h-8 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-medium">Upload Photos</h3>
                <p className="text-muted-foreground">
                  Drag and drop photos here, or click to select files
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  {photos.length}/{maxPhotos} photos uploaded
                </p>
              </div>
              <Button variant="outline" className="flex items-center space-x-2">
                <ImageIcon className="w-4 h-4" />
                <span>Choose Files</span>
              </Button>
            </div>
          </div>
        )}

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />

        {/* Photos Grid */}
        {photos.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Your Photos</h3>
              <Badge variant="secondary">
                {photos.length}/{maxPhotos} photos
              </Badge>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {photos.map((photo) => (
                <div key={photo.id} className="relative group">
                  <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                    <img
                      src={photo.url}
                      alt="Profile photo"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  {/* Photo Actions Overlay */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center space-x-2">
                    {!photo.isAvatar && (
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => onSetAsAvatar(photo.id)}
                        className="flex items-center space-x-1"
                      >
                        <Star className="w-3 h-3" />
                        <span className="text-xs">Set Avatar</span>
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => onDeletePhoto(photo.id)}
                      className="flex items-center space-x-1"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>

                  {/* Avatar Badge */}
                  {photo.isAvatar && (
                    <div className="absolute top-2 left-2">
                      <Badge className="bg-primary text-primary-foreground flex items-center space-x-1">
                        <Crown className="w-3 h-3" />
                        <span className="text-xs">Avatar</span>
                      </Badge>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {photos.length === 0 && (
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <Camera className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">No photos yet</h3>
            <p className="text-muted-foreground">
              Upload your first photo to get started
            </p>
          </div>
        )}

        {/* Photo Limit Reached */}
        {!canUploadMore && (
          <div className="bg-muted/50 rounded-lg p-4 text-center">
            <p className="text-sm text-muted-foreground">
              You've reached the maximum of {maxPhotos} photos. 
              Delete some photos to upload new ones.
            </p>
          </div>
        )}

        {/* Guidelines */}
        <div className="bg-muted/30 rounded-lg p-4">
          <h4 className="font-medium mb-2">Photo Guidelines</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Photos should be clear and well-lit</li>
            <li>• Maximum file size: 10MB per photo</li>
            <li>• Supported formats: JPG, PNG, WebP</li>
            <li>• One photo can be set as your profile avatar</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
