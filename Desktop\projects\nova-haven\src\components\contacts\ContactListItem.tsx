import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { UserActionDropdown } from "./UserActionDropdown";
import { MessageCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";

interface ContactListItemProps {
  id: string;
  name: string;
  avatar?: string;
  isOnline?: boolean;
  lastSeen?: string;
  username?: string;
}

export function ContactListItem({
  id,
  name,
  avatar,
  isOnline = false,
  lastSeen,
  username,
}: ContactListItemProps) {
  return (
    <div className="flex items-center space-x-3 p-3 hover:bg-accent/50 rounded-lg transition-colors group">
      <div className="relative">
        <Avatar className="w-12 h-12">
          <AvatarImage src={avatar} alt={name} />
          <AvatarFallback className="bg-primary/10 text-primary font-medium">
            {name.slice(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div className={cn(
          "absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 rounded-full border-2 border-background",
          isOnline ? "bg-status-online" : "bg-status-offline"
        )} />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-foreground truncate">
              {name}
            </h3>
            {username && (
              <p className="text-xs text-muted-foreground">@{username}</p>
            )}
          </div>
          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Link to={`/chats/${id}`}>
              <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                <MessageCircle className="w-4 h-4" />
              </Button>
            </Link>
            <UserActionDropdown userId={id} userName={name} />
          </div>
        </div>
        <div className="mt-1">
          {isOnline ? (
            <span className="text-xs text-status-online">Online</span>
          ) : lastSeen ? (
            <span className="text-xs text-muted-foreground">Last seen {lastSeen}</span>
          ) : (
            <span className="text-xs text-muted-foreground">Offline</span>
          )}
        </div>
      </div>
    </div>
  );
}
