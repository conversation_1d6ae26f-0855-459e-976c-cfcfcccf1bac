import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { UserPlus, Clock, MessageCircle } from "lucide-react";

interface UserSearchResultItemProps {
  id: string;
  name: string;
  avatar?: string;
  username?: string;
  mutualContacts?: number;
  isContact?: boolean;
  isPending?: boolean;
  onAddContact?: (userId: string) => void;
  onSendMessage?: (userId: string) => void;
}

export function UserSearchResultItem({
  id,
  name,
  avatar,
  username,
  mutualContacts,
  isContact = false,
  isPending = false,
  onAddContact,
  onSendMessage,
}: UserSearchResultItemProps) {
  const handleAction = () => {
    if (isContact && onSendMessage) {
      onSendMessage(id);
    } else if (!isPending && onAddContact) {
      onAddContact(id);
    }
  };

  const getButtonContent = () => {
    if (isContact) {
      return (
        <>
          <MessageCircle className="w-4 h-4 mr-2" />
          Message
        </>
      );
    } else if (isPending) {
      return (
        <>
          <Clock className="w-4 h-4 mr-2" />
          Request Sent
        </>
      );
    } else {
      return (
        <>
          <UserPlus className="w-4 h-4 mr-2" />
          Add Contact
        </>
      );
    }
  };

  return (
    <div className="flex items-center space-x-3 p-3 hover:bg-accent/50 rounded-lg transition-colors">
      <Avatar className="w-12 h-12">
        <AvatarImage src={avatar} alt={name} />
        <AvatarFallback className="bg-primary/10 text-primary font-medium">
          {name.slice(0, 2).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-foreground truncate">
              {name}
            </h3>
            {username && (
              <p className="text-xs text-muted-foreground">@{username}</p>
            )}
            {mutualContacts && mutualContacts > 0 && (
              <p className="text-xs text-muted-foreground">
                {mutualContacts} mutual contact{mutualContacts > 1 ? 's' : ''}
              </p>
            )}
          </div>
          <Button
            size="sm"
            variant={isContact ? "default" : "outline"}
            onClick={handleAction}
            disabled={isPending}
            className="min-w-[100px]"
          >
            {getButtonContent()}
          </Button>
        </div>
      </div>
    </div>
  );
}
