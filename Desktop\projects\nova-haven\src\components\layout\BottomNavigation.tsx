import { Link, useLocation } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { 
  MessageCircle, 
  Users, 
  User, 
  Compass, 
  Settings 
} from "lucide-react";

const navigation = [
  { name: "Cha<PERSON>", href: "/", icon: MessageCircle, value: "chats" },
  { name: "Contacts", href: "/contacts", icon: Users, value: "contacts" },
  { name: "Profile", href: "/profile", icon: User, value: "profile" },
  { name: "Nearby", href: "/nearby", icon: Compass, value: "nearby" },
  { name: "Settings", href: "/settings", icon: Settings, value: "settings" },
];

export function BottomNavigation() {
  const location = useLocation();
  
  const getCurrentValue = () => {
    const currentNav = navigation.find(nav => nav.href === location.pathname);
    return currentNav?.value || "chats";
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-card border-t border-border z-50">
      <Tabs value={getCurrentValue()} className="w-full">
        <TabsList className="w-full h-16 bg-transparent grid grid-cols-5 gap-0">
          {navigation.map((item) => (
            <TabsTrigger
              key={item.value}
              value={item.value}
              className="flex-col gap-1 h-full data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
              asChild
            >
              <Link to={item.href}>
                <item.icon className="w-5 h-5" />
                <span className="text-xs">{item.name}</span>
              </Link>
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
    </div>
  );
}
