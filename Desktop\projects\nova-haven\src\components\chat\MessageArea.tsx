import { useEffect, useRef, useState } from "react";
import { MessageBubble } from "./MessageBubble";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Search, 
  X, 
  ChevronUp, 
  ChevronDown,
  ArrowDown,
  Loader2
} from "lucide-react";
import { format, isToday, isYesterday, isSameDay } from "date-fns";

interface Message {
  id: string;
  content: {
    type: "text" | "image" | "video" | "file" | "location";
    text?: string;
    imageUrl?: string;
    videoUrl?: string;
    fileName?: string;
    fileUrl?: string;
    fileSize?: string;
    location?: {
      lat: number;
      lng: number;
      name: string;
      isLive?: boolean;
    };
  };
  timestamp: Date;
  isSentByMe: boolean;
  isDelivered?: boolean;
  isRead?: boolean;
  senderName?: string;
  senderAvatar?: string;
}

interface MessageAreaProps {
  messages: Message[];
  isLoading?: boolean;
  isTyping?: boolean;
  typingUser?: string;
  isGroupChat?: boolean;
  searchMode?: boolean;
  searchQuery?: string;
  searchResults?: number;
  currentSearchIndex?: number;
  onSearchQueryChange?: (query: string) => void;
  onSearchNext?: () => void;
  onSearchPrev?: () => void;
  onExitSearch?: () => void;
  onLoadMore?: () => void;
  onImageClick?: (imageUrl: string) => void;
  onFileDownload?: (fileUrl: string, fileName: string) => void;
  onLocationClick?: (location: any) => void;
}

export function MessageArea({
  messages,
  isLoading = false,
  isTyping = false,
  typingUser,
  isGroupChat = false,
  searchMode = false,
  searchQuery = "",
  searchResults = 0,
  currentSearchIndex = 0,
  onSearchQueryChange,
  onSearchNext,
  onSearchPrev,
  onExitSearch,
  onLoadMore,
  onImageClick,
  onFileDownload,
  onLocationClick,
}: MessageAreaProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isNearBottom, setIsNearBottom] = useState(true);

  // Auto-scroll to bottom when new messages arrive (only if user is near bottom)
  useEffect(() => {
    if (isNearBottom && !searchMode) {
      scrollToBottom();
    }
  }, [messages, isNearBottom, searchMode]);

  // Handle scroll position tracking
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 100;
      setIsNearBottom(isAtBottom);
      setShowScrollToBottom(!isAtBottom && messages.length > 0);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [messages.length]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getDateHeader = (currentMessage: Message, previousMessage?: Message) => {
    const currentDate = currentMessage.timestamp;
    const previousDate = previousMessage?.timestamp;

    if (!previousMessage || !isSameDay(currentDate, previousDate)) {
      let dateText: string;
      
      if (isToday(currentDate)) {
        dateText = "Today";
      } else if (isYesterday(currentDate)) {
        dateText = "Yesterday";
      } else {
        dateText = format(currentDate, "MMMM d, yyyy");
      }

      return (
        <div className="flex justify-center my-4">
          <div className="bg-muted px-3 py-1 rounded-full">
            <span className="text-xs text-muted-foreground font-medium">
              {dateText}
            </span>
          </div>
        </div>
      );
    }
    
    return null;
  };

  const shouldShowAvatar = (currentMessage: Message, nextMessage?: Message) => {
    if (isGroupChat && !currentMessage.isSentByMe) {
      return !nextMessage || 
             nextMessage.isSentByMe || 
             nextMessage.senderName !== currentMessage.senderName ||
             (nextMessage.timestamp.getTime() - currentMessage.timestamp.getTime()) > 300000; // 5 minutes
    }
    return false;
  };

  return (
    <div className="flex flex-col h-full">
      {/* Search Bar */}
      {searchMode && (
        <div className="flex items-center space-x-2 p-3 border-b border-border bg-muted/30">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search messages..."
              value={searchQuery}
              onChange={(e) => onSearchQueryChange?.(e.target.value)}
              className="pl-10 pr-4"
            />
          </div>
          
          {searchResults > 0 && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                {currentSearchIndex + 1} of {searchResults}
              </span>
              <Button variant="ghost" size="sm" onClick={onSearchPrev}>
                <ChevronUp className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={onSearchNext}>
                <ChevronDown className="w-4 h-4" />
              </Button>
            </div>
          )}
          
          <Button variant="ghost" size="sm" onClick={onExitSearch}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* Messages Container */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4 py-2 scroll-smooth"
      >
        {/* Load More Button */}
        {onLoadMore && messages.length > 0 && (
          <div className="flex justify-center py-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onLoadMore}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Loading...
                </>
              ) : (
                "Load older messages"
              )}
            </Button>
          </div>
        )}

        {/* Messages */}
        {messages.map((message, index) => {
          const previousMessage = index > 0 ? messages[index - 1] : undefined;
          const nextMessage = index < messages.length - 1 ? messages[index + 1] : undefined;
          
          return (
            <div key={message.id}>
              {getDateHeader(message, previousMessage)}
              <MessageBubble
                message={message}
                showAvatar={shouldShowAvatar(message, nextMessage)}
                isGroupChat={isGroupChat}
                onImageClick={onImageClick}
                onFileDownload={onFileDownload}
                onLocationClick={onLocationClick}
              />
            </div>
          );
        })}

        {/* Typing Indicator */}
        {isTyping && (
          <div className="flex items-center space-x-2 mb-4">
            <div className="flex items-center space-x-2 bg-muted px-4 py-2 rounded-2xl rounded-bl-md">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
            </div>
            {typingUser && (
              <span className="text-xs text-muted-foreground">
                {typingUser} is typing...
              </span>
            )}
          </div>
        )}

        {/* Empty State */}
        {messages.length === 0 && !isLoading && (
          <div className="flex flex-col items-center justify-center h-full text-center p-8">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
              <Search className="w-8 h-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium text-foreground mb-2">
              No messages yet
            </h3>
            <p className="text-muted-foreground max-w-xs">
              Start a conversation by sending your first message below.
            </p>
          </div>
        )}

        {/* Scroll anchor */}
        <div ref={messagesEndRef} />
      </div>

      {/* Scroll to Bottom Button */}
      {showScrollToBottom && (
        <div className="absolute bottom-4 right-4">
          <Button
            size="sm"
            onClick={scrollToBottom}
            className="rounded-full shadow-lg"
          >
            <ArrowDown className="w-4 h-4" />
          </Button>
        </div>
      )}
    </div>
  );
}
