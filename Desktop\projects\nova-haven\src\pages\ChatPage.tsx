import { useState, useEffect } from "react";
import { Layout } from "@/components/layout/Layout";
import { OneToOneChatView } from "@/components/chat/OneToOneChatView";
import { useParams } from "react-router-dom";

// Mock data for development
const mockUsers: Record<string, any> = {
  "1": {
    id: "1",
    name: "<PERSON>",
    username: "sarah.j",
    avatar: "/placeholder.svg",
    isOnline: true,
    isTyping: false,
  },
  "2": {
    id: "2",
    name: "<PERSON>",
    username: "alex.c",
    avatar: "/placeholder.svg",
    isOnline: false,
    lastSeen: "2 hours ago",
    isTyping: false,
  },
  "3": {
    id: "3",
    name: "<PERSON>",
    username: "maria.g",
    avatar: "/placeholder.svg",
    isOnline: true,
    isTyping: true,
  },
};

const mockMessages = [
  {
    id: "1",
    content: {
      type: "text" as const,
      text: "Hey! How are you doing today?",
    },
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    isSentByMe: false,
    isDelivered: true,
    isRead: true,
  },
  {
    id: "2",
    content: {
      type: "text" as const,
      text: "I'm doing great! Just finished a really interesting project at work. How about you?",
    },
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000 + 5 * 60 * 1000), // 1h 55m ago
    isSentByMe: true,
    isDelivered: true,
    isRead: true,
  },
  {
    id: "3",
    content: {
      type: "text" as const,
      text: "That sounds awesome! I'd love to hear more about it. 😊",
    },
    timestamp: new Date(Date.now() - 1.5 * 60 * 60 * 1000), // 1.5 hours ago
    isSentByMe: false,
    isDelivered: true,
    isRead: true,
  },
  {
    id: "4",
    content: {
      type: "image" as const,
      imageUrl: "/placeholder.svg",
      text: "Here's a photo from our team celebration! 🎉",
    },
    timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
    isSentByMe: true,
    isDelivered: true,
    isRead: true,
  },
  {
    id: "5",
    content: {
      type: "text" as const,
      text: "Wow, that looks like so much fun! Your team seems really close.",
    },
    timestamp: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
    isSentByMe: false,
    isDelivered: true,
    isRead: true,
  },
  {
    id: "6",
    content: {
      type: "file" as const,
      fileName: "project_proposal.pdf",
      fileUrl: "/placeholder.pdf",
      fileSize: "2.4 MB",
      text: "Here's the proposal I mentioned. Let me know what you think!",
    },
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    isSentByMe: true,
    isDelivered: true,
    isRead: false,
  },
  {
    id: "7",
    content: {
      type: "text" as const,
      text: "Thanks! I'll take a look at it this evening and get back to you with my thoughts.",
    },
    timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 minutes ago
    isSentByMe: false,
    isDelivered: true,
    isRead: true,
  },
  {
    id: "8",
    content: {
      type: "location" as const,
      location: {
        lat: 37.7749,
        lng: -122.4194,
        name: "Café Central, San Francisco",
      },
      text: "Want to meet here for coffee tomorrow?",
    },
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    isSentByMe: true,
    isDelivered: true,
    isRead: false,
  }
];

export default function ChatPage() {
  const { id } = useParams();
  const [messages, setMessages] = useState(mockMessages);
  const [isLoading, setIsLoading] = useState(false);
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout>();

  const user = mockUsers[id || "1"] || mockUsers["1"];

  const handleSendMessage = async (message: string, attachments?: File[]) => {
    const newMessage = {
      id: Date.now().toString(),
      content: {
        type: "text" as const,
        text: message,
      },
      timestamp: new Date(),
      isSentByMe: true,
      isDelivered: false,
      isRead: false,
    };

    // Add message immediately for UI responsiveness
    setMessages(prev => [...prev, newMessage]);

    // Simulate API call
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, isDelivered: true }
            : msg
        )
      );
    }, 1000);

    // Simulate read receipt after a delay
    setTimeout(() => {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === newMessage.id 
            ? { ...msg, isRead: true }
            : msg
        )
      );
    }, 3000);

    // Simulate response from other user (for demo purposes)
    if (Math.random() > 0.7) {
      setTimeout(() => {
        const responses = [
          "That's interesting!",
          "Thanks for sharing that 😊",
          "I agree with you on that",
          "Let me think about it",
          "Good point!",
        ];
        
        const responseMessage = {
          id: (Date.now() + 1).toString(),
          content: {
            type: "text" as const,
            text: responses[Math.floor(Math.random() * responses.length)],
          },
          timestamp: new Date(),
          isSentByMe: false,
          isDelivered: true,
          isRead: false,
        };
        
        setMessages(prev => [...prev, responseMessage]);
      }, 2000 + Math.random() * 3000);
    }

    console.log("Sending message:", message, attachments);
    // TODO: Implement actual message sending logic
  };

  const handleLoadMore = () => {
    setIsLoading(true);
    // Simulate loading older messages
    setTimeout(() => {
      const olderMessages = [
        {
          id: "older-1",
          content: {
            type: "text" as const,
            text: "This is an older message from yesterday.",
          },
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
          isSentByMe: false,
          isDelivered: true,
          isRead: true,
        },
        {
          id: "older-2",
          content: {
            type: "text" as const,
            text: "Hope you had a great weekend!",
          },
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000 + 30 * 60 * 1000),
          isSentByMe: true,
          isDelivered: true,
          isRead: true,
        },
      ];
      
      setMessages(prev => [...olderMessages, ...prev]);
      setIsLoading(false);
    }, 1000);
  };

  const handleTyping = (isTyping: boolean) => {
    console.log("User is typing:", isTyping);
    
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }
    
    if (isTyping) {
      // Send typing indicator to other user
      // TODO: Implement real-time typing indicator
      
      const timeout = setTimeout(() => {
        // Stop typing indicator after 3 seconds of inactivity
        console.log("Stopped typing");
      }, 3000);
      
      setTypingTimeout(timeout);
    }
  };

  return (
    <Layout>
      <OneToOneChatView
        chatId={id || "1"}
        user={user}
        messages={messages}
        isLoading={isLoading}
        onSendMessage={handleSendMessage}
        onLoadMore={handleLoadMore}
        onTyping={handleTyping}
      />
    </Layout>
  );
}
