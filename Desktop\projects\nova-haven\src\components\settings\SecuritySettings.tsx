import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Shield, 
  Lock, 
  Key, 
  Smartphone,
  Eye,
  EyeOff,
  Check,
  X,
  AlertTriangle,
  Clock,
  Globe
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface SecuritySettingsProps {
  settings: {
    twoFactorEnabled: boolean;
    biometricEnabled: boolean;
    sessionTimeout: number;
    showReadReceipts: boolean;
    showTypingIndicator: boolean;
    allowScreenshots: boolean;
    encryptLocalStorage: boolean;
  };
  activeSessions: Array<{
    id: string;
    device: string;
    location: string;
    lastActive: string;
    isCurrent: boolean;
  }>;
  onSettingChange: (setting: string, value: boolean | number) => void;
  onChangePassword: (currentPassword: string, newPassword: string) => void;
  onTerminateSession: (sessionId: string) => void;
  onTerminateAllSessions: () => void;
}

export function SecuritySettings({ 
  settings, 
  activeSessions,
  onSettingChange, 
  onChangePassword,
  onTerminateSession,
  onTerminateAllSessions
}: SecuritySettingsProps) {
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [showTerminateAllDialog, setShowTerminateAllDialog] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const handlePasswordChange = () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      // Handle password mismatch
      return;
    }
    onChangePassword(passwordData.currentPassword, passwordData.newPassword);
    setPasswordData({ currentPassword: "", newPassword: "", confirmPassword: "" });
    setShowChangePassword(false);
  };

  const passwordStrength = (password: string) => {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  };

  const getStrengthColor = (strength: number) => {
    if (strength < 2) return "bg-red-500";
    if (strength < 4) return "bg-yellow-500";
    return "bg-green-500";
  };

  const getStrengthText = (strength: number) => {
    if (strength < 2) return "Weak";
    if (strength < 4) return "Medium";
    return "Strong";
  };

  return (
    <>
      <div className="space-y-6">
        {/* Password & Authentication */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Lock className="w-5 h-5" />
              <span>Password & Authentication</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Password</Label>
                <p className="text-sm text-muted-foreground">
                  Last changed 3 months ago
                </p>
              </div>
              <Button 
                variant="outline" 
                onClick={() => setShowChangePassword(true)}
              >
                <Key className="w-4 h-4 mr-2" />
                Change Password
              </Button>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="twoFactorEnabled" className="flex items-center space-x-2">
                  <Shield className="w-4 h-4" />
                  <span>Two-Factor Authentication</span>
                </Label>
                <p className="text-sm text-muted-foreground">
                  Add an extra layer of security to your account
                </p>
              </div>
              <Switch
                id="twoFactorEnabled"
                checked={settings.twoFactorEnabled}
                onCheckedChange={(checked) => onSettingChange("twoFactorEnabled", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="biometricEnabled" className="flex items-center space-x-2">
                  <Smartphone className="w-4 h-4" />
                  <span>Biometric Authentication</span>
                </Label>
                <p className="text-sm text-muted-foreground">
                  Use fingerprint or face unlock (mobile only)
                </p>
              </div>
              <Switch
                id="biometricEnabled"
                checked={settings.biometricEnabled}
                onCheckedChange={(checked) => onSettingChange("biometricEnabled", checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Session Management */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <Globe className="w-5 h-5" />
                <span>Active Sessions</span>
              </CardTitle>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowTerminateAllDialog(true)}
              >
                Terminate All
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {activeSessions.map((session) => (
              <div key={session.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    <h4 className="font-medium">{session.device}</h4>
                    {session.isCurrent && (
                      <Badge variant="default" className="text-xs">Current</Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">{session.location}</p>
                  <p className="text-xs text-muted-foreground">
                    Last active: {session.lastActive}
                  </p>
                </div>
                {!session.isCurrent && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => onTerminateSession(session.id)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label className="flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>Session Timeout</span>
                </Label>
                <p className="text-sm text-muted-foreground">
                  Automatically sign out after inactivity
                </p>
              </div>
              <select
                value={settings.sessionTimeout}
                onChange={(e) => onSettingChange("sessionTimeout", parseInt(e.target.value))}
                className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
              >
                <option value={15}>15 minutes</option>
                <option value={30}>30 minutes</option>
                <option value={60}>1 hour</option>
                <option value={240}>4 hours</option>
                <option value={480}>8 hours</option>
                <option value={0}>Never</option>
              </select>
            </div>
          </CardContent>
        </Card>

        {/* Privacy Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Eye className="w-5 h-5" />
              <span>Privacy Controls</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="showReadReceipts">Read Receipts</Label>
                <p className="text-sm text-muted-foreground">
                  Let others know when you've read their messages
                </p>
              </div>
              <Switch
                id="showReadReceipts"
                checked={settings.showReadReceipts}
                onCheckedChange={(checked) => onSettingChange("showReadReceipts", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="showTypingIndicator">Typing Indicator</Label>
                <p className="text-sm text-muted-foreground">
                  Show when you're typing a message
                </p>
              </div>
              <Switch
                id="showTypingIndicator"
                checked={settings.showTypingIndicator}
                onCheckedChange={(checked) => onSettingChange("showTypingIndicator", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="allowScreenshots">Allow Screenshots</Label>
                <p className="text-sm text-muted-foreground">
                  Allow taking screenshots in the app
                </p>
              </div>
              <Switch
                id="allowScreenshots"
                checked={settings.allowScreenshots}
                onCheckedChange={(checked) => onSettingChange("allowScreenshots", checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Data Encryption */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="w-5 h-5" />
              <span>Data Encryption</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="encryptLocalStorage">Encrypt Local Storage</Label>
                <p className="text-sm text-muted-foreground">
                  Encrypt messages stored on this device
                </p>
              </div>
              <Switch
                id="encryptLocalStorage"
                checked={settings.encryptLocalStorage}
                onCheckedChange={(checked) => onSettingChange("encryptLocalStorage", checked)}
              />
            </div>

            <div className="bg-muted/30 rounded-lg p-4">
              <div className="flex space-x-3">
                <Shield className="w-5 h-5 text-green-500 mt-0.5" />
                <div>
                  <h4 className="font-medium text-sm">End-to-End Encryption</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    All messages are encrypted end-to-end by default. Only you and the recipient 
                    can read them.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Change Password Dialog */}
      <AlertDialog open={showChangePassword} onOpenChange={setShowChangePassword}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Password</AlertDialogTitle>
            <AlertDialogDescription>
              Enter your current password and choose a new secure password.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="currentPassword">Current Password</Label>
              <div className="relative">
                <Input
                  id="currentPassword"
                  type={showPasswords.current ? "text" : "password"}
                  value={passwordData.currentPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPasswords(prev => ({ ...prev, current: !prev.current }))}
                >
                  {showPasswords.current ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="newPassword">New Password</Label>
              <div className="relative">
                <Input
                  id="newPassword"
                  type={showPasswords.new ? "text" : "password"}
                  value={passwordData.newPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPasswords(prev => ({ ...prev, new: !prev.new }))}
                >
                  {showPasswords.new ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
              {passwordData.newPassword && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="flex-1 bg-muted h-2 rounded-full overflow-hidden">
                      <div 
                        className={`h-full transition-all ${getStrengthColor(passwordStrength(passwordData.newPassword))}`}
                        style={{ width: `${(passwordStrength(passwordData.newPassword) / 5) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium">
                      {getStrengthText(passwordStrength(passwordData.newPassword))}
                    </span>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm New Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showPasswords.confirm ? "text" : "password"}
                  value={passwordData.confirmPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPasswords(prev => ({ ...prev, confirm: !prev.confirm }))}
                >
                  {showPasswords.confirm ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </div>
              {passwordData.confirmPassword && (
                <div className="flex items-center space-x-2 text-sm">
                  {passwordData.newPassword === passwordData.confirmPassword ? (
                    <>
                      <Check className="w-4 h-4 text-green-500" />
                      <span className="text-green-500">Passwords match</span>
                    </>
                  ) : (
                    <>
                      <X className="w-4 h-4 text-red-500" />
                      <span className="text-red-500">Passwords don't match</span>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handlePasswordChange}
              disabled={
                !passwordData.currentPassword || 
                !passwordData.newPassword || 
                passwordData.newPassword !== passwordData.confirmPassword ||
                passwordStrength(passwordData.newPassword) < 3
              }
            >
              Change Password
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Terminate All Sessions Dialog */}
      <AlertDialog open={showTerminateAllDialog} onOpenChange={setShowTerminateAllDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-destructive" />
              <span>Terminate All Sessions</span>
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will sign you out of all devices except this one. You'll need to sign in again 
              on other devices.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={onTerminateAllSessions}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Terminate All Sessions
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
