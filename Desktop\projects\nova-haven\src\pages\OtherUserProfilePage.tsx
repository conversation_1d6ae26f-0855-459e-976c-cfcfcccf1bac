import { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { 
  ArrowLeft, 
  MessageCircle, 
  UserPlus, 
  UserX, 
  Flag,
  MapPin, 
  Calendar, 
  Globe,
  MoreHorizontal,
  Phone,
  Video,
  Users
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/alert-dialog";

// Mock user data
const mockUsers: Record<string, any> = {
  "1": {
    id: "1",
    name: "<PERSON>",
    username: "sarah.j",
    avatar: "/placeholder.svg",
    bio: "UX Designer passionate about creating meaningful digital experiences. Love hiking, photography, and good coffee ☕",
    location: "San Francisco, CA",
    birthday: "March 15, 1995",
    isOnline: true,
    joinedDate: "January 2023",
    additionalPhotos: [
      "/placeholder.svg",
      "/placeholder.svg",
      "/placeholder.svg",
      "/placeholder.svg",
    ],
    showBirthday: true,
    showGender: true,
    gender: "female",
    mutualContacts: 5,
    isContact: true,
    isBlocked: false,
  },
  "2": {
    id: "2",
    name: "Alex Chen",
    username: "alex.c",
    avatar: "/placeholder.svg",
    bio: "Software engineer by day, musician by night 🎸 Always learning something new!",
    location: "New York, NY",
    birthday: "July 22, 1992",
    isOnline: false,
    lastSeen: "2 hours ago",
    joinedDate: "March 2022",
    additionalPhotos: [
      "/placeholder.svg",
      "/placeholder.svg",
    ],
    showBirthday: false,
    showGender: true,
    gender: "male",
    mutualContacts: 3,
    isContact: false,
    isBlocked: false,
    contactRequestSent: true,
  },
  "3": {
    id: "3",
    name: "Maria Garcia",
    username: "maria.g",
    avatar: "/placeholder.svg",
    bio: "Travel enthusiast 🌍 | Food lover 🍜 | Capturing life's beautiful moments 📸",
    location: "Barcelona, Spain",
    birthday: "November 8, 1994",
    isOnline: true,
    joinedDate: "September 2023",
    additionalPhotos: [
      "/placeholder.svg",
      "/placeholder.svg",
      "/placeholder.svg",
      "/placeholder.svg",
      "/placeholder.svg",
    ],
    showBirthday: true,
    showGender: false,
    mutualContacts: 0,
    isContact: false,
    isBlocked: false,
  },
};

export default function OtherUserProfilePage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [showBlockDialog, setShowBlockDialog] = useState(false);
  const [showReportDialog, setShowReportDialog] = useState(false);
  const [userState, setUserState] = useState(() => mockUsers[id || "1"] || mockUsers["1"]);

  const handleBack = () => {
    navigate(-1);
  };

  const handleMessage = () => {
    navigate(`/chats/${userState.id}`);
  };

  const handleAddContact = () => {
    setUserState(prev => ({ ...prev, contactRequestSent: true }));
    console.log("Sending contact request to", userState.name);
    // TODO: Implement add contact functionality
  };

  const handleRemoveContact = () => {
    setUserState(prev => ({ ...prev, isContact: false }));
    console.log("Removing contact", userState.name);
    // TODO: Implement remove contact functionality
  };

  const handleCall = () => {
    console.log("Starting voice call with", userState.name);
    // TODO: Implement voice call functionality
  };

  const handleVideoCall = () => {
    console.log("Starting video call with", userState.name);
    // TODO: Implement video call functionality
  };

  const handleViewMutualContacts = () => {
    console.log("Viewing mutual contacts with", userState.name);
    // TODO: Navigate to mutual contacts page
  };

  const handleBlock = () => {
    setUserState(prev => ({ ...prev, isBlocked: true, isContact: false }));
    setShowBlockDialog(false);
    console.log("Blocking user", userState.name);
    // TODO: Implement block user functionality
  };

  const handleReport = () => {
    setShowReportDialog(false);
    console.log("Reporting user", userState.name);
    // TODO: Implement report user functionality
  };

  const handlePhotoClick = (photoIndex: number) => {
    console.log("Viewing photo", photoIndex);
    // TODO: Open photo gallery viewer
  };

  const getContactActionButton = () => {
    if (userState.isBlocked) {
      return (
        <Button variant="outline" disabled className="flex-1">
          <UserX className="w-4 h-4 mr-2" />
          Blocked
        </Button>
      );
    }

    if (userState.isContact) {
      return (
        <Button onClick={handleMessage} className="flex-1">
          <MessageCircle className="w-4 h-4 mr-2" />
          Message
        </Button>
      );
    }

    if (userState.contactRequestSent) {
      return (
        <Button variant="outline" disabled className="flex-1">
          <UserPlus className="w-4 h-4 mr-2" />
          Request Sent
        </Button>
      );
    }

    return (
      <Button onClick={handleAddContact} className="flex-1">
        <UserPlus className="w-4 h-4 mr-2" />
        Add Contact
      </Button>
    );
  };

  return (
    <>
      <Layout>
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-border">
            <div className="flex items-center space-x-3">
              <Button variant="ghost" size="sm" onClick={handleBack}>
                <ArrowLeft className="w-4 h-4" />
              </Button>
              <h1 className="text-xl font-semibold">{userState.name}</h1>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                {userState.isContact && (
                  <>
                    <DropdownMenuItem onClick={handleCall}>
                      <Phone className="w-4 h-4 mr-2" />
                      Voice Call
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleVideoCall}>
                      <Video className="w-4 h-4 mr-2" />
                      Video Call
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleRemoveContact}>
                      <UserX className="w-4 h-4 mr-2" />
                      Remove Contact
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                  </>
                )}
                {userState.mutualContacts > 0 && (
                  <DropdownMenuItem onClick={handleViewMutualContacts}>
                    <Users className="w-4 h-4 mr-2" />
                    Mutual Contacts ({userState.mutualContacts})
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem 
                  onClick={() => setShowBlockDialog(true)}
                  className="text-destructive focus:text-destructive"
                >
                  <UserX className="w-4 h-4 mr-2" />
                  Block User
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => setShowReportDialog(true)}
                  className="text-destructive focus:text-destructive"
                >
                  <Flag className="w-4 h-4 mr-2" />
                  Report User
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4 pb-20 lg:pb-4">
            <div className="max-w-2xl mx-auto space-y-6">
              {/* Main Profile Section */}
              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center space-y-4">
                    {/* Avatar */}
                    <div className="relative">
                      <Avatar className="w-32 h-32">
                        <AvatarImage src={userState.avatar} alt={userState.name} />
                        <AvatarFallback className="bg-primary/10 text-primary text-3xl font-medium">
                          {userState.name.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className={cn(
                        "absolute bottom-2 right-2 w-6 h-6 rounded-full border-4 border-background",
                        userState.isOnline ? "bg-status-online" : "bg-status-offline"
                      )} />
                    </div>

                    {/* Name and Username */}
                    <div className="space-y-2">
                      <h1 className="text-2xl font-bold text-foreground">{userState.name}</h1>
                      <p className="text-muted-foreground">@{userState.username}</p>
                      {userState.isOnline ? (
                        <Badge variant="secondary" className="bg-status-online/10 text-status-online">
                          Online
                        </Badge>
                      ) : userState.lastSeen ? (
                        <p className="text-sm text-muted-foreground">Last seen {userState.lastSeen}</p>
                      ) : (
                        <Badge variant="secondary" className="bg-status-offline/10 text-status-offline">
                          Offline
                        </Badge>
                      )}
                    </div>

                    {/* Bio */}
                    {userState.bio && (
                      <p className="text-foreground max-w-md leading-relaxed">
                        {userState.bio}
                      </p>
                    )}

                    {/* Info Section */}
                    <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
                      {userState.location && (
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4" />
                          <span>{userState.location}</span>
                        </div>
                      )}
                      {userState.showBirthday && userState.birthday && (
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{userState.birthday}</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-1">
                        <Globe className="w-4 h-4" />
                        <span>Joined {userState.joinedDate}</span>
                      </div>
                    </div>

                    {/* Mutual Contacts */}
                    {userState.mutualContacts > 0 && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={handleViewMutualContacts}
                        className="flex items-center space-x-2"
                      >
                        <Users className="w-4 h-4" />
                        <span>{userState.mutualContacts} mutual contact{userState.mutualContacts > 1 ? 's' : ''}</span>
                      </Button>
                    )}

                    {/* Action Buttons */}
                    <div className="flex space-x-3 pt-2 w-full max-w-md">
                      {getContactActionButton()}
                      {userState.isContact && (
                        <Button variant="outline" onClick={handleCall}>
                          <Phone className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Photos Grid */}
              {userState.additionalPhotos && userState.additionalPhotos.length > 0 && (
                <Card>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Photos</h3>
                      <div className="grid grid-cols-3 gap-2">
                        {userState.additionalPhotos.map((photo: string, index: number) => (
                          <div
                            key={index}
                            className="aspect-square rounded-lg overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
                            onClick={() => handlePhotoClick(index)}
                          >
                            <img
                              src={photo}
                              alt={`Photo ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </Layout>

      {/* Block User Dialog */}
      <AlertDialog open={showBlockDialog} onOpenChange={setShowBlockDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Block {userState.name}?</AlertDialogTitle>
            <AlertDialogDescription>
              This user will no longer be able to send you messages or see your online status.
              You can unblock them later from your blocked users list.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleBlock}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Block User
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Report User Dialog */}
      <AlertDialog open={showReportDialog} onOpenChange={setShowReportDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Report {userState.name}?</AlertDialogTitle>
            <AlertDialogDescription>
              Report this user for inappropriate behavior. Our team will review the report
              and take appropriate action if necessary.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleReport}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Report User
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
