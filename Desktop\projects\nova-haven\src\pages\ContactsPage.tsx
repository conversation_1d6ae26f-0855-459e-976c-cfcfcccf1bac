import { Layout } from "@/components/layout/Layout";
import { ContactsList } from "@/components/contacts/ContactsList";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, QrCode } from "lucide-react";

export default function ContactsPage() {
  return (
    <Layout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <h1 className="text-xl font-semibold">Contacts</h1>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">
              <QrCode className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Contacts List */}
        <div className="flex-1 overflow-hidden">
          <ContactsList />
        </div>
      </div>
    </Layout>
  );
}
