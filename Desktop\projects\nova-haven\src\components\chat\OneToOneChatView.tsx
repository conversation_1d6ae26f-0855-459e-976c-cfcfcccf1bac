import { useState, useCallback } from "react";
import { Chat<PERSON>eader } from "./ChatHeader";
import { MessageArea } from "./MessageArea";
import { MessageInput } from "./MessageInput";
import { useNavigate } from "react-router-dom";

interface ChatUser {
  id: string;
  name: string;
  username: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: string;
  isTyping?: boolean;
}

interface Message {
  id: string;
  content: {
    type: "text" | "image" | "video" | "file" | "location";
    text?: string;
    imageUrl?: string;
    videoUrl?: string;
    fileName?: string;
    fileUrl?: string;
    fileSize?: string;
    location?: {
      lat: number;
      lng: number;
      name: string;
      isLive?: boolean;
    };
  };
  timestamp: Date;
  isSentByMe: boolean;
  isDelivered?: boolean;
  isRead?: boolean;
  senderName?: string;
  senderAvatar?: string;
}

interface OneToOneChatViewProps {
  chatId: string;
  user: ChatUser;
  messages: Message[];
  isLoading?: boolean;
  onSendMessage: (message: string, attachments?: File[]) => void;
  onLoadMore?: () => void;
  onTyping?: (isTyping: boolean) => void;
}

export function OneToOneChatView({
  chatId,
  user,
  messages,
  isLoading = false,
  onSendMessage,
  onLoadMore,
  onTyping,
}: OneToOneChatViewProps) {
  const navigate = useNavigate();
  const [messageInput, setMessageInput] = useState("");
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [searchMode, setSearchMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Header actions
  const handleBack = () => {
    navigate("/");
  };

  const handleCall = () => {
    console.log("Starting voice call with", user.name);
    // TODO: Implement voice call functionality
  };

  const handleVideoCall = () => {
    console.log("Starting video call with", user.name);
    // TODO: Implement video call functionality
  };

  const handleViewProfile = () => {
    navigate(`/profile/${user.id}`);
  };

  const handleBlockUser = () => {
    console.log("Blocking user", user.name);
    // TODO: Implement block user functionality
  };

  const handleReportUser = () => {
    console.log("Reporting user", user.name);
    // TODO: Implement report user functionality
  };

  const handleClearChat = () => {
    console.log("Clearing chat with", user.name);
    // TODO: Implement clear chat functionality
  };

  // Message actions
  const handleSendMessage = useCallback(async (message: string, messageAttachments?: File[]) => {
    try {
      if (messageAttachments && messageAttachments.length > 0) {
        setIsUploading(true);
        // TODO: Upload attachments first
        console.log("Uploading attachments:", messageAttachments);
      }
      
      await onSendMessage(message, messageAttachments);
      setAttachments([]);
    } catch (error) {
      console.error("Failed to send message:", error);
    } finally {
      setIsUploading(false);
    }
  }, [onSendMessage]);

  const handleImageClick = (imageUrl: string) => {
    console.log("Opening image:", imageUrl);
    // TODO: Open image in full screen viewer
  };

  const handleFileDownload = (fileUrl: string, fileName: string) => {
    console.log("Downloading file:", fileName, fileUrl);
    // TODO: Implement file download
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.click();
  };

  const handleLocationClick = (location: any) => {
    console.log("Opening location:", location);
    // TODO: Open location in maps
  };

  const handleLocationShare = () => {
    console.log("Sharing location");
    // TODO: Implement location sharing
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
            name: "Current Location",
          };
          console.log("Got location:", location);
          // TODO: Send location message
        },
        (error) => {
          console.error("Error getting location:", error);
        }
      );
    }
  };

  // Search functionality
  const searchResults = searchQuery ? messages.filter(msg => 
    msg.content.text?.toLowerCase().includes(searchQuery.toLowerCase())
  ).length : 0;

  const handleSearchNext = () => {
    console.log("Search next");
    // TODO: Implement search navigation
  };

  const handleSearchPrev = () => {
    console.log("Search previous");
    // TODO: Implement search navigation
  };

  const handleExitSearch = () => {
    setSearchMode(false);
    setSearchQuery("");
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Chat Header */}
      <ChatHeader
        user={user}
        onBack={handleBack}
        onCall={handleCall}
        onVideoCall={handleVideoCall}
        onViewProfile={handleViewProfile}
        onBlockUser={handleBlockUser}
        onReportUser={handleReportUser}
        onClearChat={handleClearChat}
      />

      {/* Message Area */}
      <div className="flex-1 relative overflow-hidden">
        <MessageArea
          messages={messages}
          isLoading={isLoading}
          isTyping={user.isTyping}
          typingUser={user.isTyping ? user.name : undefined}
          isGroupChat={false}
          searchMode={searchMode}
          searchQuery={searchQuery}
          searchResults={searchResults}
          currentSearchIndex={0}
          onSearchQueryChange={setSearchQuery}
          onSearchNext={handleSearchNext}
          onSearchPrev={handleSearchPrev}
          onExitSearch={handleExitSearch}
          onLoadMore={onLoadMore}
          onImageClick={handleImageClick}
          onFileDownload={handleFileDownload}
          onLocationClick={handleLocationClick}
        />
      </div>

      {/* Message Input */}
      <MessageInput
        value={messageInput}
        onChange={setMessageInput}
        onSend={handleSendMessage}
        onTyping={onTyping}
        attachments={attachments}
        onAttachmentsChange={setAttachments}
        onLocationShare={handleLocationShare}
        isUploading={isUploading}
        placeholder={`Message ${user.name}...`}
      />
    </div>
  );
}
