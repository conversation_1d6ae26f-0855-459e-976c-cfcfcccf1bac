import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  MapPin, 
  Locate, 
  ZoomIn, 
  ZoomOut, 
  Navigation,
  RefreshCw,
  Users
} from "lucide-react";

// Mock user positions for the map
const mockMapUsers = [
  {
    id: "1",
    name: "<PERSON>",
    avatar: "/placeholder.svg",
    position: { x: 65, y: 45 },
    distance: "0.3 km",
    isOnline: true,
  },
  {
    id: "2",
    name: "<PERSON>", 
    avatar: "/placeholder.svg",
    position: { x: 35, y: 60 },
    distance: "0.8 km",
    isOnline: false,
  },
  {
    id: "3",
    name: "<PERSON>",
    avatar: "/placeholder.svg",
    position: { x: 70, y: 25 },
    distance: "1.2 km",
    isOnline: true,
  },
  {
    id: "4",
    name: "<PERSON>",
    avatar: "/placeholder.svg",
    position: { x: 25, y: 30 },
    distance: "1.5 km",
    isOnline: false,
  },
  {
    id: "5",
    name: "Jessica Park",
    avatar: "/placeholder.svg",
    position: { x: 80, y: 70 },
    distance: "2.1 km",
    isOnline: true,
  },
];

interface NearbyMapViewProps {
  onUserClick?: (userId: string) => void;
  onRefresh?: () => void;
}

export function NearbyMapView({ onUserClick, onRefresh }: NearbyMapViewProps) {
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleUserMarkerClick = (userId: string) => {
    setSelectedUser(selectedUser === userId ? null : userId);
    onUserClick?.(userId);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate location update
    setTimeout(() => {
      setIsRefreshing(false);
      onRefresh?.();
    }, 1000);
  };

  const selectedUserData = mockMapUsers.find(user => user.id === selectedUser);

  return (
    <div className="flex flex-col h-full">
      {/* Map Controls */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Users className="w-3 h-3" />
            <span>{mockMapUsers.length} nearby</span>
          </Badge>
          <Badge variant="secondary" className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-status-online rounded-full" />
            <span>{mockMapUsers.filter(u => u.isOnline).length} online</span>
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          </Button>
          <Button variant="outline" size="sm">
            <Locate className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Map Area */}
      <div className="flex-1 relative bg-gradient-to-br from-blue-50 to-green-50 dark:from-blue-950/20 dark:to-green-950/20 overflow-hidden">
        {/* Map Placeholder Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100/50 to-green-100/50 dark:from-blue-900/20 dark:to-green-900/20">
          {/* Grid Pattern */}
          <div className="absolute inset-0 opacity-20">
            <div className="grid grid-cols-8 grid-rows-6 h-full">
              {Array.from({ length: 48 }).map((_, i) => (
                <div key={i} className="border border-muted-foreground/10" />
              ))}
            </div>
          </div>
        </div>

        {/* Central Map Label */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="bg-background/80 backdrop-blur-sm rounded-lg p-6 text-center border border-border">
            <MapPin className="w-12 h-12 text-primary mx-auto mb-3" />
            <h3 className="text-lg font-semibold mb-2">Map Area</h3>
            <p className="text-sm text-muted-foreground max-w-xs">
              Integration with mapping library needed
              <br />
              (Google Maps, Mapbox, etc.)
            </p>
          </div>
        </div>

        {/* Current Location Marker (Center) */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
          <div className="relative">
            <div className="w-6 h-6 bg-primary rounded-full border-4 border-background shadow-lg" />
            <div className="absolute inset-0 w-6 h-6 bg-primary rounded-full animate-ping opacity-30" />
          </div>
        </div>

        {/* User Markers */}
        {mockMapUsers.map((user) => (
          <div
            key={user.id}
            className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer z-20"
            style={{ 
              left: `${user.position.x}%`, 
              top: `${user.position.y}%` 
            }}
            onClick={() => handleUserMarkerClick(user.id)}
          >
            <div className="relative group">
              <Avatar className={`w-8 h-8 border-2 border-background shadow-lg transition-transform hover:scale-110 ${
                selectedUser === user.id ? 'ring-2 ring-primary' : ''
              }`}>
                <AvatarImage src={user.avatar} alt={user.name} />
                <AvatarFallback className="text-xs">
                  {user.name.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              
              {/* Online Status */}
              <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background ${
                user.isOnline ? 'bg-status-online' : 'bg-status-offline'
              }`} />

              {/* Hover Tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                <div className="bg-popover text-popover-foreground text-xs px-2 py-1 rounded shadow-lg whitespace-nowrap">
                  {user.name} • {user.distance}
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Map Controls Overlay */}
        <div className="absolute top-4 right-4 flex flex-col space-y-2">
          <Button variant="outline" size="sm" className="bg-background/80 backdrop-blur-sm">
            <ZoomIn className="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" className="bg-background/80 backdrop-blur-sm">
            <ZoomOut className="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" className="bg-background/80 backdrop-blur-sm">
            <Navigation className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Selected User Info */}
      {selectedUserData && (
        <div className="border-t border-border p-4 bg-card">
          <div className="flex items-center space-x-3">
            <Avatar className="w-12 h-12">
              <AvatarImage src={selectedUserData.avatar} alt={selectedUserData.name} />
              <AvatarFallback>
                {selectedUserData.name.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h4 className="font-medium">{selectedUserData.name}</h4>
              <p className="text-sm text-muted-foreground">
                {selectedUserData.distance} away • {selectedUserData.isOnline ? 'Online' : 'Offline'}
              </p>
            </div>
            <div className="flex space-x-2">
              <Button size="sm" variant="outline">
                View Profile
              </Button>
              <Button size="sm">
                Message
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
