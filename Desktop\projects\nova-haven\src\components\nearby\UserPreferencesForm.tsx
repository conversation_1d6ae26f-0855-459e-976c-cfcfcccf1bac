import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { 
  Filter, 
  X, 
  Users, 
  MapPin, 
  Heart,
  Calendar,
  User
} from "lucide-react";

const preferencesSchema = z.object({
  genderPreference: z.enum(["any", "male", "female", "other"]),
  ageRange: z.array(z.number()).length(2),
  maxDistance: z.number().min(1).max(100),
  showOnlineOnly: z.boolean(),
  showWithPhotosOnly: z.boolean(),
  showMutualContactsOnly: z.boolean(),
});

type PreferencesFormData = z.infer<typeof preferencesSchema>;

interface UserPreferencesFormProps {
  initialPreferences: PreferencesFormData;
  onSave: (preferences: PreferencesFormData) => void;
  onClose: () => void;
}

export function UserPreferencesForm({ 
  initialPreferences, 
  onSave, 
  onClose 
}: UserPreferencesFormProps) {
  const [ageRange, setAgeRange] = useState(initialPreferences.ageRange);
  const [maxDistance, setMaxDistance] = useState([initialPreferences.maxDistance]);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    setValue,
    watch,
  } = useForm<PreferencesFormData>({
    resolver: zodResolver(preferencesSchema),
    defaultValues: initialPreferences,
  });

  const watchedGender = watch("genderPreference");
  const watchedShowOnlineOnly = watch("showOnlineOnly");
  const watchedShowWithPhotosOnly = watch("showWithPhotosOnly");
  const watchedShowMutualContactsOnly = watch("showMutualContactsOnly");

  const onSubmit = async (data: PreferencesFormData) => {
    try {
      const formData = {
        ...data,
        ageRange,
        maxDistance: maxDistance[0],
      };
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error("Failed to save preferences:", error);
    }
  };

  const handleReset = () => {
    const defaults = {
      genderPreference: "any" as const,
      ageRange: [18, 99],
      maxDistance: 10,
      showOnlineOnly: false,
      showWithPhotosOnly: false,
      showMutualContactsOnly: false,
    };
    
    setValue("genderPreference", defaults.genderPreference);
    setValue("showOnlineOnly", defaults.showOnlineOnly);
    setValue("showWithPhotosOnly", defaults.showWithPhotosOnly);
    setValue("showMutualContactsOnly", defaults.showMutualContactsOnly);
    setAgeRange(defaults.ageRange);
    setMaxDistance([defaults.maxDistance]);
  };

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Filter className="w-5 h-5" />
            <span>Search Preferences</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Gender Preference */}
          <div className="space-y-3">
            <Label className="flex items-center space-x-2">
              <User className="w-4 h-4" />
              <span>Gender Preference</span>
            </Label>
            <RadioGroup
              value={watchedGender}
              onValueChange={(value) => setValue("genderPreference", value as any)}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="any" id="any" />
                <Label htmlFor="any">Any</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="male" id="male" />
                <Label htmlFor="male">Male</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="female" id="female" />
                <Label htmlFor="female">Female</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="other" id="other" />
                <Label htmlFor="other">Other</Label>
              </div>
            </RadioGroup>
          </div>

          <Separator />

          {/* Age Range */}
          <div className="space-y-4">
            <Label className="flex items-center space-x-2">
              <Calendar className="w-4 h-4" />
              <span>Age Range</span>
            </Label>
            <div className="px-2">
              <Slider
                value={ageRange}
                onValueChange={setAgeRange}
                min={18}
                max={99}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-sm text-muted-foreground mt-2">
                <span>{ageRange[0]} years</span>
                <span>{ageRange[1]} years</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Distance Range */}
          <div className="space-y-4">
            <Label className="flex items-center space-x-2">
              <MapPin className="w-4 h-4" />
              <span>Maximum Distance</span>
            </Label>
            <div className="px-2">
              <Slider
                value={maxDistance}
                onValueChange={setMaxDistance}
                min={1}
                max={100}
                step={1}
                className="w-full"
              />
              <div className="flex justify-center text-sm text-muted-foreground mt-2">
                <span>{maxDistance[0]} km</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Additional Filters */}
          <div className="space-y-4">
            <Label className="flex items-center space-x-2">
              <Heart className="w-4 h-4" />
              <span>Additional Filters</span>
            </Label>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="showOnlineOnly">Show online users only</Label>
                  <p className="text-xs text-muted-foreground">
                    Only show users who are currently online
                  </p>
                </div>
                <Switch
                  id="showOnlineOnly"
                  checked={watchedShowOnlineOnly}
                  onCheckedChange={(checked) => setValue("showOnlineOnly", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="showWithPhotosOnly">Show users with photos only</Label>
                  <p className="text-xs text-muted-foreground">
                    Only show users who have profile photos
                  </p>
                </div>
                <Switch
                  id="showWithPhotosOnly"
                  checked={watchedShowWithPhotosOnly}
                  onCheckedChange={(checked) => setValue("showWithPhotosOnly", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="showMutualContactsOnly">Show mutual contacts only</Label>
                  <p className="text-xs text-muted-foreground">
                    Only show users with mutual contacts
                  </p>
                </div>
                <Switch
                  id="showMutualContactsOnly"
                  checked={watchedShowMutualContactsOnly}
                  onCheckedChange={(checked) => setValue("showMutualContactsOnly", checked)}
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <Button type="submit" disabled={isSubmitting} className="flex-1">
              <Filter className="w-4 h-4 mr-2" />
              {isSubmitting ? "Applying..." : "Apply Filters"}
            </Button>
            <Button type="button" variant="outline" onClick={handleReset}>
              Reset
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
