import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Shield, Eye, EyeOff, Clock, Users, MessageCircle } from "lucide-react";

interface PrivacySettingsProps {
  settings: {
    hideOnlineStatus: boolean;
    hideLastSeen: boolean;
    allowMessagesFromContacts: boolean;
    allowContactRequests: boolean;
    showProfileToNearby: boolean;
    showBirthday: boolean;
    showGender: boolean;
  };
  onSettingChange: (setting: string, value: boolean) => void;
}

export function PrivacySettings({ settings, onSettingChange }: PrivacySettingsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Shield className="w-5 h-5" />
          <span>Privacy Settings</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Online Status & Activity */}
        <div className="space-y-4">
          <h3 className="text-base font-medium">Online Status & Activity</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="hideOnlineStatus" className="flex items-center space-x-2">
                  <Eye className="w-4 h-4" />
                  <span>Hide Online Status</span>
                </Label>
                <p className="text-sm text-muted-foreground">
                  Others won't see when you're online or offline
                </p>
              </div>
              <Switch
                id="hideOnlineStatus"
                checked={settings.hideOnlineStatus}
                onCheckedChange={(checked) => onSettingChange("hideOnlineStatus", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="hideLastSeen" className="flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <span>Hide Last Seen Status</span>
                </Label>
                <p className="text-sm text-muted-foreground">
                  Others won't see when you were last active
                </p>
              </div>
              <Switch
                id="hideLastSeen"
                checked={settings.hideLastSeen}
                onCheckedChange={(checked) => onSettingChange("hideLastSeen", checked)}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Communication */}
        <div className="space-y-4">
          <h3 className="text-base font-medium">Communication</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="allowMessagesFromContacts" className="flex items-center space-x-2">
                  <MessageCircle className="w-4 h-4" />
                  <span>Allow Messages from Contacts Only</span>
                </Label>
                <p className="text-sm text-muted-foreground">
                  Only your contacts can send you messages
                </p>
              </div>
              <Switch
                id="allowMessagesFromContacts"
                checked={settings.allowMessagesFromContacts}
                onCheckedChange={(checked) => onSettingChange("allowMessagesFromContacts", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="allowContactRequests" className="flex items-center space-x-2">
                  <Users className="w-4 h-4" />
                  <span>Allow Contact Requests</span>
                </Label>
                <p className="text-sm text-muted-foreground">
                  Others can send you contact requests
                </p>
              </div>
              <Switch
                id="allowContactRequests"
                checked={settings.allowContactRequests}
                onCheckedChange={(checked) => onSettingChange("allowContactRequests", checked)}
              />
            </div>
          </div>
        </div>

        <Separator />

        {/* Profile Visibility */}
        <div className="space-y-4">
          <h3 className="text-base font-medium">Profile Visibility</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="showProfileToNearby" className="flex items-center space-x-2">
                  <Users className="w-4 h-4" />
                  <span>Show Profile in Nearby</span>
                </Label>
                <p className="text-sm text-muted-foreground">
                  Your profile will appear to nearby users
                </p>
              </div>
              <Switch
                id="showProfileToNearby"
                checked={settings.showProfileToNearby}
                onCheckedChange={(checked) => onSettingChange("showProfileToNearby", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="showBirthday" className="flex items-center space-x-2">
                  <EyeOff className="w-4 h-4" />
                  <span>Show Birthday</span>
                </Label>
                <p className="text-sm text-muted-foreground">
                  Your birthday will be visible on your profile
                </p>
              </div>
              <Switch
                id="showBirthday"
                checked={settings.showBirthday}
                onCheckedChange={(checked) => onSettingChange("showBirthday", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="showGender" className="flex items-center space-x-2">
                  <EyeOff className="w-4 h-4" />
                  <span>Show Gender</span>
                </Label>
                <p className="text-sm text-muted-foreground">
                  Your gender will be visible on your profile
                </p>
              </div>
              <Switch
                id="showGender"
                checked={settings.showGender}
                onCheckedChange={(checked) => onSettingChange("showGender", checked)}
              />
            </div>
          </div>
        </div>

        {/* Security Notice */}
        <div className="bg-muted/30 rounded-lg p-4">
          <div className="flex space-x-3">
            <Shield className="w-5 h-5 text-primary mt-0.5" />
            <div>
              <h4 className="font-medium text-sm">Privacy Notice</h4>
              <p className="text-sm text-muted-foreground mt-1">
                These settings help control your privacy, but remember that profile information 
                you share in group chats may still be visible to other members.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
