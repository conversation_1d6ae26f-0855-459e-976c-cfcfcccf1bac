import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Edit, Camera, MapPin, Calendar, Globe } from "lucide-react";
import { cn } from "@/lib/utils";

interface UserProfile {
  id: string;
  name: string;
  username: string;
  avatar?: string;
  bio?: string;
  location?: string;
  birthday?: string;
  isOnline: boolean;
  lastSeen?: string;
  joinedDate: string;
  additionalPhotos: string[];
  showBirthday: boolean;
  showGender: boolean;
  gender?: "male" | "female" | "other";
}

interface UserProfileDisplayProps {
  profile: UserProfile;
  isOwnProfile?: boolean;
  onEditProfile?: () => void;
  onAddPhotos?: () => void;
  onPhotoClick?: (photoIndex: number) => void;
}

export function UserProfileDisplay({
  profile,
  isOwnProfile = false,
  onEditProfile,
  onAddPhotos,
  onPhotoClick,
}: UserProfileDisplayProps) {
  return (
    <div className="space-y-6">
      {/* Main Profile Section */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col items-center text-center space-y-4">
            {/* Avatar */}
            <div className="relative">
              <Avatar className="w-32 h-32">
                <AvatarImage src={profile.avatar} alt={profile.name} />
                <AvatarFallback className="bg-primary/10 text-primary text-3xl font-medium">
                  {profile.name.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className={cn(
                "absolute bottom-2 right-2 w-6 h-6 rounded-full border-4 border-background",
                profile.isOnline ? "bg-status-online" : "bg-status-offline"
              )} />
            </div>

            {/* Name and Username */}
            <div className="space-y-2">
              <h1 className="text-2xl font-bold text-foreground">{profile.name}</h1>
              <p className="text-muted-foreground">@{profile.username}</p>
              {profile.isOnline ? (
                <Badge variant="secondary" className="bg-status-online/10 text-status-online">
                  Online
                </Badge>
              ) : profile.lastSeen ? (
                <p className="text-sm text-muted-foreground">Last seen {profile.lastSeen}</p>
              ) : (
                <Badge variant="secondary" className="bg-status-offline/10 text-status-offline">
                  Offline
                </Badge>
              )}
            </div>

            {/* Bio */}
            {profile.bio && (
              <p className="text-foreground max-w-md leading-relaxed">
                {profile.bio}
              </p>
            )}

            {/* Info Section */}
            <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
              {profile.location && (
                <div className="flex items-center space-x-1">
                  <MapPin className="w-4 h-4" />
                  <span>{profile.location}</span>
                </div>
              )}
              {profile.showBirthday && profile.birthday && (
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>{profile.birthday}</span>
                </div>
              )}
              <div className="flex items-center space-x-1">
                <Globe className="w-4 h-4" />
                <span>Joined {profile.joinedDate}</span>
              </div>
            </div>

            {/* Action Buttons */}
            {isOwnProfile && (
              <div className="flex space-x-3 pt-2">
                <Button onClick={onEditProfile} className="flex items-center space-x-2">
                  <Edit className="w-4 h-4" />
                  <span>Edit Profile</span>
                </Button>
                <Button variant="outline" onClick={onAddPhotos} className="flex items-center space-x-2">
                  <Camera className="w-4 h-4" />
                  <span>Add Photos</span>
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Additional Photos Grid */}
      {profile.additionalPhotos.length > 0 && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Photos</h3>
                {isOwnProfile && (
                  <Button variant="ghost" size="sm" onClick={onAddPhotos}>
                    <Camera className="w-4 h-4 mr-2" />
                    Add More
                  </Button>
                )}
              </div>
              <div className="grid grid-cols-3 gap-2">
                {profile.additionalPhotos.map((photo, index) => (
                  <div
                    key={index}
                    className="aspect-square rounded-lg overflow-hidden cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={() => onPhotoClick?.(index)}
                  >
                    <img
                      src={photo}
                      alt={`Photo ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty Photos Placeholder */}
      {profile.additionalPhotos.length === 0 && isOwnProfile && (
        <Card>
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
                <Camera className="w-8 h-8 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2">No photos yet</h3>
                <p className="text-muted-foreground mb-4">
                  Add photos to show more about yourself
                </p>
                <Button onClick={onAddPhotos} className="flex items-center space-x-2">
                  <Camera className="w-4 h-4" />
                  <span>Add Photos</span>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
