import { useState } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ContactListItem } from "./ContactListItem";
import { UserSearchResultItem } from "./UserSearchResultItem";
import { PendingContactRequests } from "./PendingContactRequests";
import { BlockedUsersList } from "./BlockedUsersList";
import { Search, Users, UserPlus, UserX } from "lucide-react";
import { useNavigate } from "react-router-dom";

// Mock data for development
const mockContacts = [
  {
    id: "1",
    name: "<PERSON>",
    avatar: "/placeholder.svg",
    username: "sarah.j",
    isOnline: true,
  },
  {
    id: "2",
    name: "<PERSON>",
    avatar: "/placeholder.svg",
    username: "alexc",
    isOnline: true,
  },
  {
    id: "3",
    name: "<PERSON>",
    avatar: "/placeholder.svg",
    username: "maria.g",
    isOnline: false,
    lastSeen: "2 hours ago",
  },
  {
    id: "4",
    name: "<PERSON> Doe",
    avatar: "/placeholder.svg",
    username: "johndoe",
    isOnline: false,
    lastSeen: "1 day ago",
  },
];

const mockSearchResults = [
  {
    id: "5",
    name: "<PERSON> <PERSON>",
    avatar: "/placeholder.svg",
    username: "emily.w",
    mutualContacts: 2,
    isContact: false,
    isPending: false,
  },
  {
    id: "6",
    name: "David Brown",
    avatar: "/placeholder.svg",
    username: "david.b",
    mutualContacts: 1,
    isContact: false,
    isPending: true,
  },
];

const mockPendingRequests = [
  {
    id: "7",
    name: "Jessica Taylor",
    avatar: "/placeholder.svg",
    username: "jess.t",
    sentAt: "2 hours ago",
    mutualContacts: 3,
  },
  {
    id: "8",
    name: "Michael Lee",
    avatar: "/placeholder.svg",
    username: "mike.lee",
    sentAt: "1 day ago",
    mutualContacts: 1,
  },
];

const mockBlockedUsers = [
  {
    id: "9",
    name: "Spam User",
    avatar: "/placeholder.svg",
    username: "spammer",
    blockedAt: "1 week ago",
  },
];

export function ContactsList() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("contacts");
  const navigate = useNavigate();

  const filteredContacts = mockContacts.filter(contact =>
    contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    contact.username.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredSearchResults = mockSearchResults.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.username.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleAddContact = (userId: string) => {
    console.log("Adding contact:", userId);
    // TODO: Implement add contact functionality
  };

  const handleSendMessage = (userId: string) => {
    navigate(`/chats/${userId}`);
  };

  const handleAcceptRequest = (requestId: string) => {
    console.log("Accepting request:", requestId);
    // TODO: Implement accept request functionality
  };

  const handleRejectRequest = (requestId: string) => {
    console.log("Rejecting request:", requestId);
    // TODO: Implement reject request functionality
  };

  const handleUnblockUser = (userId: string) => {
    console.log("Unblocking user:", userId);
    // TODO: Implement unblock user functionality
  };

  return (
    <div className="flex flex-col h-full">
      {/* Search */}
      <div className="p-4 border-b border-border">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search contacts or users..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="w-full justify-start p-1 m-4 mb-0">
          <TabsTrigger value="contacts" className="flex items-center space-x-2">
            <Users className="w-4 h-4" />
            <span>Contacts</span>
          </TabsTrigger>
          <TabsTrigger value="search" className="flex items-center space-x-2">
            <Search className="w-4 h-4" />
            <span>Find Users</span>
          </TabsTrigger>
          <TabsTrigger value="requests" className="flex items-center space-x-2">
            <UserPlus className="w-4 h-4" />
            <span>Requests</span>
          </TabsTrigger>
          <TabsTrigger value="blocked" className="flex items-center space-x-2">
            <UserX className="w-4 h-4" />
            <span>Blocked</span>
          </TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-y-auto pb-20 lg:pb-4">
          <TabsContent value="contacts" className="mt-0 p-4">
            {filteredContacts.length > 0 ? (
              <div className="space-y-1">
                {filteredContacts.map((contact) => (
                  <ContactListItem
                    key={contact.id}
                    id={contact.id}
                    name={contact.name}
                    avatar={contact.avatar}
                    username={contact.username}
                    isOnline={contact.isOnline}
                    lastSeen={contact.lastSeen}
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                  <Users className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium text-foreground mb-2">
                  {searchQuery ? "No contacts found" : "No contacts yet"}
                </h3>
                <p className="text-muted-foreground max-w-xs">
                  {searchQuery 
                    ? `No contacts match "${searchQuery}"`
                    : "Start adding contacts to see them here"
                  }
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="search" className="mt-0 p-4">
            {searchQuery ? (
              filteredSearchResults.length > 0 ? (
                <div className="space-y-1">
                  {filteredSearchResults.map((user) => (
                    <UserSearchResultItem
                      key={user.id}
                      id={user.id}
                      name={user.name}
                      avatar={user.avatar}
                      username={user.username}
                      mutualContacts={user.mutualContacts}
                      isContact={user.isContact}
                      isPending={user.isPending}
                      onAddContact={handleAddContact}
                      onSendMessage={handleSendMessage}
                    />
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center h-64 text-center">
                  <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                    <Search className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium text-foreground mb-2">
                    No users found
                  </h3>
                  <p className="text-muted-foreground max-w-xs">
                    No users match "{searchQuery}". Try a different search term.
                  </p>
                </div>
              )
            ) : (
              <div className="flex flex-col items-center justify-center h-64 text-center">
                <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                  <Search className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium text-foreground mb-2">
                  Find new contacts
                </h3>
                <p className="text-muted-foreground max-w-xs">
                  Search for users by name or username to add them as contacts.
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="requests" className="mt-0 p-4">
            <PendingContactRequests
              requests={mockPendingRequests}
              onAcceptRequest={handleAcceptRequest}
              onRejectRequest={handleRejectRequest}
            />
          </TabsContent>

          <TabsContent value="blocked" className="mt-0 p-4">
            <BlockedUsersList
              blockedUsers={mockBlockedUsers}
              onUnblockUser={handleUnblockUser}
            />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
