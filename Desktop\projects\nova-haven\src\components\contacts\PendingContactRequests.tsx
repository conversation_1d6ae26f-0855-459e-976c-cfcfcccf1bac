import { Avatar, Avatar<PERSON>allback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Check, X, UserPlus } from "lucide-react";

interface ContactRequest {
  id: string;
  name: string;
  avatar?: string;
  username?: string;
  sentAt: string;
  mutualContacts?: number;
}

interface PendingContactRequestsProps {
  requests: ContactRequest[];
  onAcceptRequest?: (requestId: string) => void;
  onRejectRequest?: (requestId: string) => void;
}

export function PendingContactRequests({
  requests,
  onAcceptRequest,
  onRejectRequest,
}: PendingContactRequestsProps) {
  if (requests.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <UserPlus className="w-5 h-5 text-muted-foreground" />
        <h3 className="font-medium text-foreground">
          Pending Requests ({requests.length})
        </h3>
      </div>
      
      <div className="space-y-2">
        {requests.map((request) => (
          <div
            key={request.id}
            className="flex items-center space-x-3 p-3 bg-accent/30 rounded-lg border border-border"
          >
            <Avatar className="w-12 h-12">
              <AvatarImage src={request.avatar} alt={request.name} />
              <AvatarFallback className="bg-primary/10 text-primary font-medium">
                {request.name.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div>
                <h4 className="font-medium text-foreground truncate">
                  {request.name}
                </h4>
                {request.username && (
                  <p className="text-xs text-muted-foreground">@{request.username}</p>
                )}
                <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
                  <span>Sent {request.sentAt}</span>
                  {request.mutualContacts && request.mutualContacts > 0 && (
                    <>
                      <span>•</span>
                      <span>{request.mutualContacts} mutual contact{request.mutualContacts > 1 ? 's' : ''}</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => onRejectRequest?.(request.id)}
                className="h-8 w-8 p-0 text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                <X className="w-4 h-4" />
              </Button>
              <Button
                size="sm"
                onClick={() => onAcceptRequest?.(request.id)}
                className="h-8 w-8 p-0"
              >
                <Check className="w-4 h-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
