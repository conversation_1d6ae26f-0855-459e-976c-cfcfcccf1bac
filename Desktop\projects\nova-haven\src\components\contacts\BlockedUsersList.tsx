import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { UserX, Unlock } from "lucide-react";

interface BlockedUser {
  id: string;
  name: string;
  avatar?: string;
  username?: string;
  blockedAt: string;
}

interface BlockedUsersListProps {
  blockedUsers: BlockedUser[];
  onUnblockUser?: (userId: string) => void;
}

export function BlockedUsersList({
  blockedUsers,
  onUnblockUser,
}: BlockedUsersListProps) {
  if (blockedUsers.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
          <UserX className="w-8 h-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-medium text-foreground mb-2">
          No blocked users
        </h3>
        <p className="text-muted-foreground max-w-xs mx-auto">
          Users you block will appear here. You can unblock them at any time.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <UserX className="w-5 h-5 text-muted-foreground" />
        <h3 className="font-medium text-foreground">
          Blocked Users ({blockedUsers.length})
        </h3>
      </div>
      
      <div className="space-y-2">
        {blockedUsers.map((user) => (
          <div
            key={user.id}
            className="flex items-center space-x-3 p-3 bg-muted/30 rounded-lg border border-border"
          >
            <Avatar className="w-12 h-12 opacity-60">
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback className="bg-muted text-muted-foreground font-medium">
                {user.name.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div>
                <h4 className="font-medium text-foreground truncate opacity-60">
                  {user.name}
                </h4>
                {user.username && (
                  <p className="text-xs text-muted-foreground opacity-60">@{user.username}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Blocked {user.blockedAt}
                </p>
              </div>
            </div>
            
            <Button
              size="sm"
              variant="outline"
              onClick={() => onUnblockUser?.(user.id)}
              className="min-w-[80px]"
            >
              <Unlock className="w-4 h-4 mr-2" />
              Unblock
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}
