import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  Bell, 
  MessageCircle, 
  Phone, 
  Users, 
  Volume2,
  VolumeX,
  Smartphone,
  Monitor
} from "lucide-react";

interface NotificationSettingsProps {
  settings: {
    pushNotifications: boolean;
    messageNotifications: boolean;
    callNotifications: boolean;
    groupNotifications: boolean;
    contactRequestNotifications: boolean;
    soundEnabled: boolean;
    vibrationEnabled: boolean;
    desktopNotifications: boolean;
    showMessagePreview: boolean;
    quietHoursEnabled: boolean;
    quietHoursStart: string;
    quietHoursEnd: string;
  };
  onSettingChange: (setting: string, value: boolean | string) => void;
}

export function NotificationSettings({ settings, onSettingChange }: NotificationSettingsProps) {
  const handleTestNotification = () => {
    if ("Notification" in window && Notification.permission === "granted") {
      new Notification("Test Notification", {
        body: "This is how notifications will appear",
        icon: "/placeholder.svg"
      });
    } else if ("Notification" in window && Notification.permission !== "denied") {
      Notification.requestPermission().then(permission => {
        if (permission === "granted") {
          new Notification("Test Notification", {
            body: "This is how notifications will appear",
            icon: "/placeholder.svg"
          });
        }
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Notification Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bell className="w-5 h-5" />
            <span>Notifications</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="pushNotifications" className="flex items-center space-x-2">
                <Smartphone className="w-4 h-4" />
                <span>Push Notifications</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Receive notifications on this device
              </p>
            </div>
            <Switch
              id="pushNotifications"
              checked={settings.pushNotifications}
              onCheckedChange={(checked) => onSettingChange("pushNotifications", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="desktopNotifications" className="flex items-center space-x-2">
                <Monitor className="w-4 h-4" />
                <span>Desktop Notifications</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Show notifications on desktop/browser
              </p>
            </div>
            <Switch
              id="desktopNotifications"
              checked={settings.desktopNotifications}
              onCheckedChange={(checked) => onSettingChange("desktopNotifications", checked)}
            />
          </div>

          <div className="flex items-center justify-center pt-2">
            <Button variant="outline" size="sm" onClick={handleTestNotification}>
              Test Notification
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Notification Types */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Types</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="messageNotifications" className="flex items-center space-x-2">
                <MessageCircle className="w-4 h-4" />
                <span>Messages</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Notifications for new messages
              </p>
            </div>
            <Switch
              id="messageNotifications"
              checked={settings.messageNotifications}
              onCheckedChange={(checked) => onSettingChange("messageNotifications", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="callNotifications" className="flex items-center space-x-2">
                <Phone className="w-4 h-4" />
                <span>Calls</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Notifications for incoming calls
              </p>
            </div>
            <Switch
              id="callNotifications"
              checked={settings.callNotifications}
              onCheckedChange={(checked) => onSettingChange("callNotifications", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="groupNotifications" className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>Group Activities</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Notifications for group messages and activities
              </p>
            </div>
            <Switch
              id="groupNotifications"
              checked={settings.groupNotifications}
              onCheckedChange={(checked) => onSettingChange("groupNotifications", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="contactRequestNotifications" className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>Contact Requests</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Notifications for new contact requests
              </p>
            </div>
            <Switch
              id="contactRequestNotifications"
              checked={settings.contactRequestNotifications}
              onCheckedChange={(checked) => onSettingChange("contactRequestNotifications", checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Sound & Vibration */}
      <Card>
        <CardHeader>
          <CardTitle>Sound & Vibration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="soundEnabled" className="flex items-center space-x-2">
                <Volume2 className="w-4 h-4" />
                <span>Notification Sounds</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Play sound for notifications
              </p>
            </div>
            <Switch
              id="soundEnabled"
              checked={settings.soundEnabled}
              onCheckedChange={(checked) => onSettingChange("soundEnabled", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="vibrationEnabled" className="flex items-center space-x-2">
                <Smartphone className="w-4 h-4" />
                <span>Vibration</span>
              </Label>
              <p className="text-sm text-muted-foreground">
                Vibrate for notifications (mobile only)
              </p>
            </div>
            <Switch
              id="vibrationEnabled"
              checked={settings.vibrationEnabled}
              onCheckedChange={(checked) => onSettingChange("vibrationEnabled", checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Privacy & Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Privacy & Preview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="showMessagePreview">Show Message Preview</Label>
              <p className="text-sm text-muted-foreground">
                Show message content in notifications
              </p>
            </div>
            <Switch
              id="showMessagePreview"
              checked={settings.showMessagePreview}
              onCheckedChange={(checked) => onSettingChange("showMessagePreview", checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Quiet Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <VolumeX className="w-5 h-5" />
            <span>Quiet Hours</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="quietHoursEnabled">Enable Quiet Hours</Label>
              <p className="text-sm text-muted-foreground">
                Disable notifications during specified hours
              </p>
            </div>
            <Switch
              id="quietHoursEnabled"
              checked={settings.quietHoursEnabled}
              onCheckedChange={(checked) => onSettingChange("quietHoursEnabled", checked)}
            />
          </div>

          {settings.quietHoursEnabled && (
            <>
              <Separator />
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quietHoursStart">Start Time</Label>
                  <input
                    type="time"
                    id="quietHoursStart"
                    value={settings.quietHoursStart}
                    onChange={(e) => onSettingChange("quietHoursStart", e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="quietHoursEnd">End Time</Label>
                  <input
                    type="time"
                    id="quietHoursEnd"
                    value={settings.quietHoursEnd}
                    onChange={(e) => onSettingChange("quietHoursEnd", e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
                  />
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
